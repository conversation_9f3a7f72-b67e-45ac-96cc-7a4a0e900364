/**
 * 输入节点预设
 * 提供各种输入处理节点
 */

import { EventNode } from '../nodes/EventNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 按键按下节点
 */
export class KeyDownNode extends EventNode {
  private keyCode: string = '';
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '按键按下';
    this.metadata.description = '检测按键按下事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'keyCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 输出插槽
    this.addOutput({
      name: 'keyEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '按键事件'
    });

    this.addOutput({
      name: 'keyState',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '按键状态'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.keyCode = this.getInputValue('keyCode') || 'Space';
    this.isListening = true;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === this.keyCode) {
        const keyEvent = {
          code: event.code,
          key: event.key,
          timestamp: Date.now(),
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        this.setOutputValue('keyEvent', keyEvent);
        this.setOutputValue('keyState', true);
        this.triggerEvent('keyDown', keyEvent);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    // 存储事件处理器以便后续清理
    (this as any)._keyDownHandler = handleKeyDown;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._keyDownHandler) {
      document.removeEventListener('keydown', (this as any)._keyDownHandler);
      delete (this as any)._keyDownHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 按键释放节点
 */
export class KeyUpNode extends EventNode {
  private keyCode: string = '';
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '按键释放';
    this.metadata.description = '检测按键释放事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'keyCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 输出插槽
    this.addOutput({
      name: 'keyEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '按键事件'
    });

    this.addOutput({
      name: 'keyState',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '按键状态'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.keyCode = this.getInputValue('keyCode') || 'Space';
    this.isListening = true;

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.code === this.keyCode) {
        const keyEvent = {
          code: event.code,
          key: event.key,
          timestamp: Date.now(),
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        this.setOutputValue('keyEvent', keyEvent);
        this.setOutputValue('keyState', false);
        this.triggerEvent('keyUp', keyEvent);
      }
    };

    document.addEventListener('keyup', handleKeyUp);
    
    // 存储事件处理器以便后续清理
    (this as any)._keyUpHandler = handleKeyUp;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._keyUpHandler) {
      document.removeEventListener('keyup', (this as any)._keyUpHandler);
      delete (this as any)._keyUpHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 获取按键状态节点
 */
export class GetKeyStateNode extends FunctionNode {
  private keyStates: Map<string, boolean> = new Map();
  private keyDownTimes: Map<string, number> = new Map();
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '获取按键状态';
    this.metadata.description = '获取指定按键的当前状态';
    this.metadata.category = 'Input';

    this.initializeKeyListener();
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'keyCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码',
      required: true
    });

    // 输出插槽
    this.addOutput({
      name: 'isPressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下'
    });

    this.addOutput({
      name: 'pressDuration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '按下时长（毫秒）'
    });
  }

  private initializeKeyListener(): void {
    if (this.isListening) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (!this.keyStates.get(event.code)) {
        this.keyStates.set(event.code, true);
        this.keyDownTimes.set(event.code, Date.now());
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      this.keyStates.set(event.code, false);
      this.keyDownTimes.delete(event.code);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    // 存储事件处理器
    (this as any)._keyDownHandler = handleKeyDown;
    (this as any)._keyUpHandler = handleKeyUp;
    this.isListening = true;
  }

  public execute(): any {
    try {
      const keyCode = this.getInputValue('keyCode');

      if (!keyCode) {
        console.error('GetKeyStateNode: 缺少按键代码');
        return null;
      }

      const isPressed = this.keyStates.get(keyCode) || false;
      const downTime = this.keyDownTimes.get(keyCode);
      const pressDuration = isPressed && downTime ? Date.now() - downTime : 0;

      this.setOutputValue('isPressed', isPressed);
      this.setOutputValue('pressDuration', pressDuration);

      return {
        isPressed,
        pressDuration
      };
    } catch (error) {
      console.error('GetKeyStateNode: 执行错误', error);
      return null;
    }
  }

  public destroy(): void {
    if ((this as any)._keyDownHandler) {
      document.removeEventListener('keydown', (this as any)._keyDownHandler);
      delete (this as any)._keyDownHandler;
    }

    if ((this as any)._keyUpHandler) {
      document.removeEventListener('keyup', (this as any)._keyUpHandler);
      delete (this as any)._keyUpHandler;
    }

    this.isListening = false;
    super.destroy();
  }
}

/**
 * 鼠标按下节点
 */
export class MouseDownNode extends EventNode {
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '鼠标按下';
    this.metadata.description = '检测鼠标按下事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '鼠标按钮 (0=左键, 1=中键, 2=右键)',
      defaultValue: 0
    });

    // 输出插槽
    this.addOutput({
      name: 'mouseEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标事件'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标位置'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '按下的按钮'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    const targetButton = this.getInputValue('button') || 0;
    this.isListening = true;

    const handleMouseDown = (event: MouseEvent) => {
      if (event.button === targetButton) {
        const mouseEvent = {
          button: event.button,
          clientX: event.clientX,
          clientY: event.clientY,
          pageX: event.pageX,
          pageY: event.pageY,
          timestamp: Date.now(),
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        const position = {
          x: event.clientX,
          y: event.clientY
        };

        this.setOutputValue('mouseEvent', mouseEvent);
        this.setOutputValue('position', position);
        this.setOutputValue('button', event.button);
        this.triggerEvent('mouseDown', mouseEvent);
      }
    };

    document.addEventListener('mousedown', handleMouseDown);
    (this as any)._mouseDownHandler = handleMouseDown;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._mouseDownHandler) {
      document.removeEventListener('mousedown', (this as any)._mouseDownHandler);
      delete (this as any)._mouseDownHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 鼠标移动节点
 */
export class MouseMoveNode extends EventNode {
  private isListening: boolean = false;
  private lastPosition: { x: number; y: number } = { x: 0, y: 0 };

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '鼠标移动';
    this.metadata.description = '检测鼠标移动事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输出插槽
    this.addOutput({
      name: 'mouseEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标事件'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标位置'
    });

    this.addOutput({
      name: 'delta',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '移动增量'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.isListening = true;

    const handleMouseMove = (event: MouseEvent) => {
      const currentPosition = {
        x: event.clientX,
        y: event.clientY
      };

      const delta = {
        x: currentPosition.x - this.lastPosition.x,
        y: currentPosition.y - this.lastPosition.y
      };

      const mouseEvent = {
        clientX: event.clientX,
        clientY: event.clientY,
        pageX: event.pageX,
        pageY: event.pageY,
        movementX: event.movementX,
        movementY: event.movementY,
        timestamp: Date.now()
      };

      this.setOutputValue('mouseEvent', mouseEvent);
      this.setOutputValue('position', currentPosition);
      this.setOutputValue('delta', delta);
      this.triggerEvent('mouseMove', mouseEvent);

      this.lastPosition = currentPosition;
    };

    document.addEventListener('mousemove', handleMouseMove);
    (this as any)._mouseMoveHandler = handleMouseMove;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._mouseMoveHandler) {
      document.removeEventListener('mousemove', (this as any)._mouseMoveHandler);
      delete (this as any)._mouseMoveHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 注册输入节点到节点注册表
 */
export function registerInputNodes(registry: any): void {
  // 键盘输入节点
  registry.registerNodeType({
    type: 'input/keyDown',
    category: 'Input',
    description: '检测按键按下事件',
    constructor: KeyDownNode,
    icon: '⌨️',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'input/keyUp',
    category: 'Input',
    description: '检测按键释放事件',
    constructor: KeyUpNode,
    icon: '⌨️',
    color: '#F44336'
  });

  registry.registerNodeType({
    type: 'input/getKeyState',
    category: 'Input',
    description: '获取指定按键的当前状态',
    constructor: GetKeyStateNode,
    icon: '⌨️',
    color: '#2196F3'
  });

  // 鼠标输入节点
  registry.registerNodeType({
    type: 'input/mouseDown',
    category: 'Input',
    description: '检测鼠标按下事件',
    constructor: MouseDownNode,
    icon: '🖱️',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'input/mouseMove',
    category: 'Input',
    description: '检测鼠标移动事件',
    constructor: MouseMoveNode,
    icon: '🖱️',
    color: '#FF9800'
  });
}
