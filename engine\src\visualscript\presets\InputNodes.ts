/**
 * 输入节点预设
 * 提供各种输入处理节点
 */

import { EventNode } from '../nodes/EventNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 按键按下节点
 */
export class KeyDownNode extends EventNode {
  private keyCode: string = '';
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '按键按下';
    this.metadata.description = '检测按键按下事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'keyCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 输出插槽
    this.addOutput({
      name: 'keyEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '按键事件'
    });

    this.addOutput({
      name: 'keyState',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '按键状态'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.keyCode = this.getInputValue('keyCode') || 'Space';
    this.isListening = true;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === this.keyCode) {
        const keyEvent = {
          code: event.code,
          key: event.key,
          timestamp: Date.now(),
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        this.setOutputValue('keyEvent', keyEvent);
        this.setOutputValue('keyState', true);
        this.triggerEvent('keyDown', keyEvent);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    // 存储事件处理器以便后续清理
    (this as any)._keyDownHandler = handleKeyDown;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._keyDownHandler) {
      document.removeEventListener('keydown', (this as any)._keyDownHandler);
      delete (this as any)._keyDownHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 按键释放节点
 */
export class KeyUpNode extends EventNode {
  private keyCode: string = '';
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '按键释放';
    this.metadata.description = '检测按键释放事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'keyCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 输出插槽
    this.addOutput({
      name: 'keyEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '按键事件'
    });

    this.addOutput({
      name: 'keyState',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '按键状态'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.keyCode = this.getInputValue('keyCode') || 'Space';
    this.isListening = true;

    const handleKeyUp = (event: KeyboardEvent) => {
      if (event.code === this.keyCode) {
        const keyEvent = {
          code: event.code,
          key: event.key,
          timestamp: Date.now(),
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        this.setOutputValue('keyEvent', keyEvent);
        this.setOutputValue('keyState', false);
        this.triggerEvent('keyUp', keyEvent);
      }
    };

    document.addEventListener('keyup', handleKeyUp);
    
    // 存储事件处理器以便后续清理
    (this as any)._keyUpHandler = handleKeyUp;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._keyUpHandler) {
      document.removeEventListener('keyup', (this as any)._keyUpHandler);
      delete (this as any)._keyUpHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 获取按键状态节点
 */
export class GetKeyStateNode extends FunctionNode {
  private keyStates: Map<string, boolean> = new Map();
  private keyDownTimes: Map<string, number> = new Map();
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '获取按键状态';
    this.metadata.description = '获取指定按键的当前状态';
    this.metadata.category = 'Input';

    this.initializeKeyListener();
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'keyCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码',
      required: true
    });

    // 输出插槽
    this.addOutput({
      name: 'isPressed',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否按下'
    });

    this.addOutput({
      name: 'pressDuration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '按下时长（毫秒）'
    });
  }

  private initializeKeyListener(): void {
    if (this.isListening) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (!this.keyStates.get(event.code)) {
        this.keyStates.set(event.code, true);
        this.keyDownTimes.set(event.code, Date.now());
      }
    };

    const handleKeyUp = (event: KeyboardEvent) => {
      this.keyStates.set(event.code, false);
      this.keyDownTimes.delete(event.code);
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    // 存储事件处理器
    (this as any)._keyDownHandler = handleKeyDown;
    (this as any)._keyUpHandler = handleKeyUp;
    this.isListening = true;
  }

  public execute(): any {
    try {
      const keyCode = this.getInputValue('keyCode');

      if (!keyCode) {
        console.error('GetKeyStateNode: 缺少按键代码');
        return null;
      }

      const isPressed = this.keyStates.get(keyCode) || false;
      const downTime = this.keyDownTimes.get(keyCode);
      const pressDuration = isPressed && downTime ? Date.now() - downTime : 0;

      this.setOutputValue('isPressed', isPressed);
      this.setOutputValue('pressDuration', pressDuration);

      return {
        isPressed,
        pressDuration
      };
    } catch (error) {
      console.error('GetKeyStateNode: 执行错误', error);
      return null;
    }
  }

  public destroy(): void {
    if ((this as any)._keyDownHandler) {
      document.removeEventListener('keydown', (this as any)._keyDownHandler);
      delete (this as any)._keyDownHandler;
    }

    if ((this as any)._keyUpHandler) {
      document.removeEventListener('keyup', (this as any)._keyUpHandler);
      delete (this as any)._keyUpHandler;
    }

    this.isListening = false;
    super.destroy();
  }
}

/**
 * 鼠标按下节点
 */
export class MouseDownNode extends EventNode {
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '鼠标按下';
    this.metadata.description = '检测鼠标按下事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '鼠标按钮 (0=左键, 1=中键, 2=右键)',
      defaultValue: 0
    });

    // 输出插槽
    this.addOutput({
      name: 'mouseEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标事件'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标位置'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '按下的按钮'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    const targetButton = this.getInputValue('button') || 0;
    this.isListening = true;

    const handleMouseDown = (event: MouseEvent) => {
      if (event.button === targetButton) {
        const mouseEvent = {
          button: event.button,
          clientX: event.clientX,
          clientY: event.clientY,
          pageX: event.pageX,
          pageY: event.pageY,
          timestamp: Date.now(),
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        const position = {
          x: event.clientX,
          y: event.clientY
        };

        this.setOutputValue('mouseEvent', mouseEvent);
        this.setOutputValue('position', position);
        this.setOutputValue('button', event.button);
        this.triggerEvent('mouseDown', mouseEvent);
      }
    };

    document.addEventListener('mousedown', handleMouseDown);
    (this as any)._mouseDownHandler = handleMouseDown;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._mouseDownHandler) {
      document.removeEventListener('mousedown', (this as any)._mouseDownHandler);
      delete (this as any)._mouseDownHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 鼠标移动节点
 */
export class MouseMoveNode extends EventNode {
  private isListening: boolean = false;
  private lastPosition: { x: number; y: number } = { x: 0, y: 0 };

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '鼠标移动';
    this.metadata.description = '检测鼠标移动事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输出插槽
    this.addOutput({
      name: 'mouseEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标事件'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标位置'
    });

    this.addOutput({
      name: 'delta',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '移动增量'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.isListening = true;

    const handleMouseMove = (event: MouseEvent) => {
      const currentPosition = {
        x: event.clientX,
        y: event.clientY
      };

      const delta = {
        x: currentPosition.x - this.lastPosition.x,
        y: currentPosition.y - this.lastPosition.y
      };

      const mouseEvent = {
        clientX: event.clientX,
        clientY: event.clientY,
        pageX: event.pageX,
        pageY: event.pageY,
        movementX: event.movementX,
        movementY: event.movementY,
        timestamp: Date.now()
      };

      this.setOutputValue('mouseEvent', mouseEvent);
      this.setOutputValue('position', currentPosition);
      this.setOutputValue('delta', delta);
      this.triggerEvent('mouseMove', mouseEvent);

      this.lastPosition = currentPosition;
    };

    document.addEventListener('mousemove', handleMouseMove);
    (this as any)._mouseMoveHandler = handleMouseMove;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._mouseMoveHandler) {
      document.removeEventListener('mousemove', (this as any)._mouseMoveHandler);
      delete (this as any)._mouseMoveHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 按键按压节点
 */
export class KeyPressNode extends EventNode {
  private keyCode: string = '';
  private isListening: boolean = false;
  private repeatInterval: number = 100;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '按键按压';
    this.metadata.description = '检测按键按压事件（支持重复）';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'keyCode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '按键代码',
      defaultValue: 'Space'
    });

    this.addInput({
      name: 'repeatInterval',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '重复间隔（毫秒）',
      defaultValue: 100
    });

    // 输出插槽
    this.addOutput({
      name: 'keyEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '按键事件'
    });

    this.addOutput({
      name: 'character',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '字符输入'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.keyCode = this.getInputValue('keyCode') || 'Space';
    this.repeatInterval = this.getInputValue('repeatInterval') || 100;
    this.isListening = true;

    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.code === this.keyCode) {
        const keyEvent = {
          code: event.code,
          key: event.key,
          timestamp: Date.now(),
          repeat: event.repeat,
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        this.setOutputValue('keyEvent', keyEvent);
        this.setOutputValue('character', event.key);
        this.triggerEvent('keyPress', keyEvent);
      }
    };

    document.addEventListener('keypress', handleKeyPress);
    (this as any)._keyPressHandler = handleKeyPress;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._keyPressHandler) {
      document.removeEventListener('keypress', (this as any)._keyPressHandler);
      delete (this as any)._keyPressHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 组合键节点
 */
export class KeyCombinationNode extends EventNode {
  private mainKey: string = '';
  private modifierKeys: string[] = [];
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '组合键';
    this.metadata.description = '检测组合键事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'mainKey',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '主键',
      required: true
    });

    this.addInput({
      name: 'modifierKeys',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '修饰键列表 (ctrl, shift, alt)',
      defaultValue: []
    });

    // 输出插槽
    this.addOutput({
      name: 'combinationEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '组合键事件'
    });

    this.addOutput({
      name: 'matched',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否匹配'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.mainKey = this.getInputValue('mainKey') || '';
    this.modifierKeys = this.getInputValue('modifierKeys') || [];
    this.isListening = true;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.code === this.mainKey) {
        const requiredCtrl = this.modifierKeys.includes('ctrl');
        const requiredShift = this.modifierKeys.includes('shift');
        const requiredAlt = this.modifierKeys.includes('alt');

        const matched =
          event.ctrlKey === requiredCtrl &&
          event.shiftKey === requiredShift &&
          event.altKey === requiredAlt;

        if (matched) {
          const combinationEvent = {
            mainKey: this.mainKey,
            modifierKeys: this.modifierKeys,
            timestamp: Date.now(),
            ctrlKey: event.ctrlKey,
            shiftKey: event.shiftKey,
            altKey: event.altKey
          };

          this.setOutputValue('combinationEvent', combinationEvent);
          this.setOutputValue('matched', true);
          this.triggerEvent('combination', combinationEvent);

          event.preventDefault();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    (this as any)._combinationHandler = handleKeyDown;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._combinationHandler) {
      document.removeEventListener('keydown', (this as any)._combinationHandler);
      delete (this as any)._combinationHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 鼠标释放节点
 */
export class MouseUpNode extends EventNode {
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '鼠标释放';
    this.metadata.description = '检测鼠标释放事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '鼠标按钮 (0=左键, 1=中键, 2=右键)',
      defaultValue: 0
    });

    // 输出插槽
    this.addOutput({
      name: 'mouseEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标事件'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '鼠标位置'
    });

    this.addOutput({
      name: 'button',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '释放的按钮'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    const targetButton = this.getInputValue('button') || 0;
    this.isListening = true;

    const handleMouseUp = (event: MouseEvent) => {
      if (event.button === targetButton) {
        const mouseEvent = {
          button: event.button,
          clientX: event.clientX,
          clientY: event.clientY,
          pageX: event.pageX,
          pageY: event.pageY,
          timestamp: Date.now(),
          ctrlKey: event.ctrlKey,
          shiftKey: event.shiftKey,
          altKey: event.altKey
        };

        const position = {
          x: event.clientX,
          y: event.clientY
        };

        this.setOutputValue('mouseEvent', mouseEvent);
        this.setOutputValue('position', position);
        this.setOutputValue('button', event.button);
        this.triggerEvent('mouseUp', mouseEvent);
      }
    };

    document.addEventListener('mouseup', handleMouseUp);
    (this as any)._mouseUpHandler = handleMouseUp;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._mouseUpHandler) {
      document.removeEventListener('mouseup', (this as any)._mouseUpHandler);
      delete (this as any)._mouseUpHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 鼠标滚轮节点
 */
export class MouseWheelNode extends EventNode {
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '鼠标滚轮';
    this.metadata.description = '检测鼠标滚轮事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输出插槽
    this.addOutput({
      name: 'wheelEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '滚轮事件'
    });

    this.addOutput({
      name: 'delta',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '滚动增量'
    });

    this.addOutput({
      name: 'direction',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '滚动方向 (up, down)'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.isListening = true;

    const handleWheel = (event: WheelEvent) => {
      const wheelEvent = {
        deltaX: event.deltaX,
        deltaY: event.deltaY,
        deltaZ: event.deltaZ,
        deltaMode: event.deltaMode,
        clientX: event.clientX,
        clientY: event.clientY,
        timestamp: Date.now()
      };

      const delta = event.deltaY;
      const direction = delta > 0 ? 'down' : 'up';

      this.setOutputValue('wheelEvent', wheelEvent);
      this.setOutputValue('delta', Math.abs(delta));
      this.setOutputValue('direction', direction);
      this.triggerEvent('wheel', wheelEvent);
    };

    document.addEventListener('wheel', handleWheel);
    (this as any)._wheelHandler = handleWheel;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._wheelHandler) {
      document.removeEventListener('wheel', (this as any)._wheelHandler);
      delete (this as any)._wheelHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 获取鼠标位置节点
 */
export class GetMousePositionNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '获取鼠标位置';
    this.metadata.description = '获取当前鼠标位置';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'coordinateSystem',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '坐标系类型 (screen, client, page)',
      defaultValue: 'client'
    });

    // 输出插槽
    this.addOutput({
      name: 'screenPosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '屏幕坐标'
    });

    this.addOutput({
      name: 'clientPosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '客户端坐标'
    });

    this.addOutput({
      name: 'pagePosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '页面坐标'
    });
  }

  public execute(): any {
    try {
      // 注意：这个方法只能在鼠标事件中获取准确位置
      // 这里提供一个基础实现，实际使用时需要结合鼠标事件
      const coordinateSystem = this.getInputValue('coordinateSystem') || 'client';

      // 从全局鼠标状态获取位置（如果可用）
      const globalMouseState = (window as any).globalMouseState || { x: 0, y: 0 };

      const screenPosition = { x: globalMouseState.screenX || 0, y: globalMouseState.screenY || 0 };
      const clientPosition = { x: globalMouseState.x || 0, y: globalMouseState.y || 0 };
      const pagePosition = { x: globalMouseState.pageX || 0, y: globalMouseState.pageY || 0 };

      this.setOutputValue('screenPosition', screenPosition);
      this.setOutputValue('clientPosition', clientPosition);
      this.setOutputValue('pagePosition', pagePosition);

      return {
        screenPosition,
        clientPosition,
        pagePosition
      };
    } catch (error) {
      console.error('GetMousePositionNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 触摸开始节点
 */
export class TouchStartNode extends EventNode {
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '触摸开始';
    this.metadata.description = '检测触摸开始事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'touchIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '触摸索引',
      defaultValue: 0
    });

    // 输出插槽
    this.addOutput({
      name: 'touchEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '触摸事件'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '触摸位置'
    });

    this.addOutput({
      name: 'touchId',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '触摸ID'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    const touchIndex = this.getInputValue('touchIndex') || 0;
    this.isListening = true;

    const handleTouchStart = (event: TouchEvent) => {
      if (event.touches.length > touchIndex) {
        const touch = event.touches[touchIndex];
        const touchEvent = {
          touchId: touch.identifier,
          clientX: touch.clientX,
          clientY: touch.clientY,
          pageX: touch.pageX,
          pageY: touch.pageY,
          screenX: touch.screenX,
          screenY: touch.screenY,
          radiusX: touch.radiusX,
          radiusY: touch.radiusY,
          rotationAngle: touch.rotationAngle,
          force: touch.force,
          timestamp: Date.now()
        };

        const position = {
          x: touch.clientX,
          y: touch.clientY
        };

        this.setOutputValue('touchEvent', touchEvent);
        this.setOutputValue('position', position);
        this.setOutputValue('touchId', touch.identifier);
        this.triggerEvent('touchStart', touchEvent);
      }
    };

    document.addEventListener('touchstart', handleTouchStart);
    (this as any)._touchStartHandler = handleTouchStart;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._touchStartHandler) {
      document.removeEventListener('touchstart', (this as any)._touchStartHandler);
      delete (this as any)._touchStartHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 触摸移动节点
 */
export class TouchMoveNode extends EventNode {
  private isListening: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '触摸移动';
    this.metadata.description = '检测触摸移动事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'touchIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '触摸索引',
      defaultValue: 0
    });

    // 输出插槽
    this.addOutput({
      name: 'touchEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '触摸事件'
    });

    this.addOutput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '触摸位置'
    });

    this.addOutput({
      name: 'delta',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '移动增量'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    const touchIndex = this.getInputValue('touchIndex') || 0;
    this.isListening = true;
    let lastPosition = { x: 0, y: 0 };

    const handleTouchMove = (event: TouchEvent) => {
      if (event.touches.length > touchIndex) {
        const touch = event.touches[touchIndex];
        const currentPosition = {
          x: touch.clientX,
          y: touch.clientY
        };

        const delta = {
          x: currentPosition.x - lastPosition.x,
          y: currentPosition.y - lastPosition.y
        };

        const touchEvent = {
          touchId: touch.identifier,
          clientX: touch.clientX,
          clientY: touch.clientY,
          pageX: touch.pageX,
          pageY: touch.pageY,
          timestamp: Date.now()
        };

        this.setOutputValue('touchEvent', touchEvent);
        this.setOutputValue('position', currentPosition);
        this.setOutputValue('delta', delta);
        this.triggerEvent('touchMove', touchEvent);

        lastPosition = currentPosition;
      }
    };

    document.addEventListener('touchmove', handleTouchMove);
    (this as any)._touchMoveHandler = handleTouchMove;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._touchMoveHandler) {
      document.removeEventListener('touchmove', (this as any)._touchMoveHandler);
      delete (this as any)._touchMoveHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 触摸结束节点
 */
export class TouchEndNode extends EventNode {
  private isListening: boolean = false;
  private startTime: number = 0;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '触摸结束';
    this.metadata.description = '检测触摸结束事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'touchIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '触摸索引',
      defaultValue: 0
    });

    // 输出插槽
    this.addOutput({
      name: 'touchEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '触摸事件'
    });

    this.addOutput({
      name: 'finalPosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '最终位置'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '持续时间（毫秒）'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    const touchIndex = this.getInputValue('touchIndex') || 0;
    this.isListening = true;
    this.startTime = Date.now();

    const handleTouchEnd = (event: TouchEvent) => {
      if (event.changedTouches.length > touchIndex) {
        const touch = event.changedTouches[touchIndex];
        const endTime = Date.now();
        const duration = endTime - this.startTime;

        const touchEvent = {
          touchId: touch.identifier,
          clientX: touch.clientX,
          clientY: touch.clientY,
          pageX: touch.pageX,
          pageY: touch.pageY,
          timestamp: endTime
        };

        const finalPosition = {
          x: touch.clientX,
          y: touch.clientY
        };

        this.setOutputValue('touchEvent', touchEvent);
        this.setOutputValue('finalPosition', finalPosition);
        this.setOutputValue('duration', duration);
        this.triggerEvent('touchEnd', touchEvent);
      }
    };

    document.addEventListener('touchend', handleTouchEnd);
    (this as any)._touchEndHandler = handleTouchEnd;
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if ((this as any)._touchEndHandler) {
      document.removeEventListener('touchend', (this as any)._touchEndHandler);
      delete (this as any)._touchEndHandler;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 游戏手柄输入节点
 */
export class GamepadInputNode extends EventNode {
  private isListening: boolean = false;
  private gamepadIndex: number = 0;
  private pollInterval: number | null = null;

  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '游戏手柄输入';
    this.metadata.description = '检测游戏手柄输入事件';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'gamepadIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '手柄索引',
      defaultValue: 0
    });

    this.addInput({
      name: 'inputType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '输入类型 (button, axis)',
      defaultValue: 'button'
    });

    this.addInput({
      name: 'inputIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '按钮/摇杆索引',
      defaultValue: 0
    });

    // 输出插槽
    this.addOutput({
      name: 'inputEvent',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '输入事件'
    });

    this.addOutput({
      name: 'inputValue',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '输入值'
    });

    this.addOutput({
      name: 'gamepadState',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '手柄状态'
    });
  }

  public startListening(): void {
    if (this.isListening) return;

    this.gamepadIndex = this.getInputValue('gamepadIndex') || 0;
    const inputType = this.getInputValue('inputType') || 'button';
    const inputIndex = this.getInputValue('inputIndex') || 0;
    this.isListening = true;

    // 游戏手柄需要轮询检测
    this.pollInterval = window.setInterval(() => {
      const gamepads = navigator.getGamepads();
      const gamepad = gamepads[this.gamepadIndex];

      if (gamepad) {
        let inputValue = 0;
        let inputEvent: any = null;

        if (inputType === 'button' && gamepad.buttons[inputIndex]) {
          const button = gamepad.buttons[inputIndex];
          inputValue = button.value;

          if (button.pressed) {
            inputEvent = {
              type: 'button',
              index: inputIndex,
              value: inputValue,
              pressed: button.pressed,
              timestamp: Date.now()
            };
          }
        } else if (inputType === 'axis' && gamepad.axes[inputIndex] !== undefined) {
          inputValue = gamepad.axes[inputIndex];

          if (Math.abs(inputValue) > 0.1) { // 死区处理
            inputEvent = {
              type: 'axis',
              index: inputIndex,
              value: inputValue,
              timestamp: Date.now()
            };
          }
        }

        if (inputEvent) {
          const gamepadState = {
            id: gamepad.id,
            index: gamepad.index,
            connected: gamepad.connected,
            timestamp: gamepad.timestamp
          };

          this.setOutputValue('inputEvent', inputEvent);
          this.setOutputValue('inputValue', inputValue);
          this.setOutputValue('gamepadState', gamepadState);
          this.triggerEvent('gamepadInput', inputEvent);
        }
      }
    }, 16); // ~60fps
  }

  public stopListening(): void {
    if (!this.isListening) return;

    if (this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
    }

    this.isListening = false;
  }

  public execute(): any {
    this.startListening();
    return null;
  }
}

/**
 * 游戏手柄震动节点
 */
export class GamepadVibrationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '游戏手柄震动';
    this.metadata.description = '控制游戏手柄震动';
    this.metadata.category = 'Input';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'gamepadIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '手柄索引',
      defaultValue: 0
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '震动强度 (0-1)',
      defaultValue: 0.5
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '持续时间（毫秒）',
      defaultValue: 200
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const gamepadIndex = inputs.gamepadIndex || 0;
      const intensity = Math.max(0, Math.min(1, inputs.intensity || 0.5));
      const duration = inputs.duration || 200;

      const gamepads = navigator.getGamepads();
      const gamepad = gamepads[gamepadIndex];

      if (!gamepad) {
        console.error('GamepadVibrationNode: 未找到指定的游戏手柄');
        return 'failed';
      }

      if (!gamepad.vibrationActuator) {
        console.error('GamepadVibrationNode: 游戏手柄不支持震动');
        return 'failed';
      }

      // 执行震动
      gamepad.vibrationActuator.playEffect('dual-rumble', {
        startDelay: 0,
        duration: duration,
        weakMagnitude: intensity,
        strongMagnitude: intensity
      }).then(() => {
        console.log('GamepadVibrationNode: 震动执行成功');
      }).catch((error: any) => {
        console.error('GamepadVibrationNode: 震动执行失败', error);
      });

      return 'success';
    } catch (error) {
      console.error('GamepadVibrationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 注册输入节点到节点注册表
 */
export function registerInputNodes(registry: any): void {
  // 键盘输入节点
  registry.registerNodeType({
    type: 'input/keyDown',
    category: 'Input',
    description: '检测按键按下事件',
    constructor: KeyDownNode,
    icon: '⌨️',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'input/keyUp',
    category: 'Input',
    description: '检测按键释放事件',
    constructor: KeyUpNode,
    icon: '⌨️',
    color: '#F44336'
  });

  registry.registerNodeType({
    type: 'input/getKeyState',
    category: 'Input',
    description: '获取指定按键的当前状态',
    constructor: GetKeyStateNode,
    icon: '⌨️',
    color: '#2196F3'
  });

  // 鼠标输入节点
  registry.registerNodeType({
    type: 'input/mouseDown',
    category: 'Input',
    description: '检测鼠标按下事件',
    constructor: MouseDownNode,
    icon: '🖱️',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'input/mouseMove',
    category: 'Input',
    description: '检测鼠标移动事件',
    constructor: MouseMoveNode,
    icon: '🖱️',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'input/keyPress',
    category: 'Input',
    description: '检测按键按压事件（支持重复）',
    constructor: KeyPressNode,
    icon: '⌨️',
    color: '#9C27B0'
  });

  registry.registerNodeType({
    type: 'input/keyCombination',
    category: 'Input',
    description: '检测组合键事件',
    constructor: KeyCombinationNode,
    icon: '⌨️',
    color: '#3F51B5'
  });

  registry.registerNodeType({
    type: 'input/mouseUp',
    category: 'Input',
    description: '检测鼠标释放事件',
    constructor: MouseUpNode,
    icon: '🖱️',
    color: '#F44336'
  });

  registry.registerNodeType({
    type: 'input/mouseWheel',
    category: 'Input',
    description: '检测鼠标滚轮事件',
    constructor: MouseWheelNode,
    icon: '🖱️',
    color: '#607D8B'
  });

  registry.registerNodeType({
    type: 'input/getMousePosition',
    category: 'Input',
    description: '获取当前鼠标位置',
    constructor: GetMousePositionNode,
    icon: '🖱️',
    color: '#795548'
  });

  // 触摸输入节点
  registry.registerNodeType({
    type: 'input/touchStart',
    category: 'Input',
    description: '检测触摸开始事件',
    constructor: TouchStartNode,
    icon: '👆',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'input/touchMove',
    category: 'Input',
    description: '检测触摸移动事件',
    constructor: TouchMoveNode,
    icon: '👆',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'input/touchEnd',
    category: 'Input',
    description: '检测触摸结束事件',
    constructor: TouchEndNode,
    icon: '👆',
    color: '#F44336'
  });

  // 游戏手柄节点
  registry.registerNodeType({
    type: 'input/gamepadInput',
    category: 'Input',
    description: '检测游戏手柄输入事件',
    constructor: GamepadInputNode,
    icon: '🎮',
    color: '#E91E63'
  });

  registry.registerNodeType({
    type: 'input/gamepadVibration',
    category: 'Input',
    description: '控制游戏手柄震动',
    constructor: GamepadVibrationNode,
    icon: '🎮',
    color: '#9C27B0'
  });
}
