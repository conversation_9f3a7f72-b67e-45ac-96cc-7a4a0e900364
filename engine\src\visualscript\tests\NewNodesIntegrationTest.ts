/**
 * 新节点集成测试
 * 测试新开发的动画、输入、逻辑和渲染节点的集成情况
 */

import { NodeRegistry } from '../nodes/NodeRegistry';
import { registerAllNodes } from '../presets/OptimizedNodeRegistry';
import { PlayAnimationNode, StopAnimationNode, GetAnimationTimeNode } from '../presets/AnimationNodes';
import { KeyDownNode, MouseDownNode, GetKeyStateNode } from '../presets/InputNodes';
import { ComparisonNode, LogicalOperationNode } from '../presets/LogicNodes';
import { SetRenderModeNode, ScreenshotNode } from '../presets/RenderingNodes';

describe('新节点集成测试', () => {
  let nodeRegistry: NodeRegistry;

  beforeEach(() => {
    nodeRegistry = new NodeRegistry();
  });

  describe('节点注册测试', () => {
    test('应该成功注册所有新节点', () => {
      // 注册所有节点
      registerAllNodes(nodeRegistry, {
        enableAnimationNodes: true,
        enableInputNodes: true,
        enableLogicNodes: true,
        enableRenderingNodes: true,
        debugMode: true
      });

      // 验证动画节点注册
      expect(nodeRegistry.hasNodeType('animation/playAnimation')).toBe(true);
      expect(nodeRegistry.hasNodeType('animation/stopAnimation')).toBe(true);
      expect(nodeRegistry.hasNodeType('animation/getAnimationTime')).toBe(true);

      // 验证输入节点注册
      expect(nodeRegistry.hasNodeType('input/keyDown')).toBe(true);
      expect(nodeRegistry.hasNodeType('input/mouseDown')).toBe(true);
      expect(nodeRegistry.hasNodeType('input/getKeyState')).toBe(true);

      // 验证逻辑节点注册
      expect(nodeRegistry.hasNodeType('logic/comparison')).toBe(true);
      expect(nodeRegistry.hasNodeType('logic/logicalOperation')).toBe(true);

      // 验证渲染节点注册
      expect(nodeRegistry.hasNodeType('rendering/setRenderMode')).toBe(true);
      expect(nodeRegistry.hasNodeType('rendering/screenshot')).toBe(true);
    });

    test('应该正确获取节点类型信息', () => {
      registerAllNodes(nodeRegistry, {
        enableAnimationNodes: true,
        enableInputNodes: true,
        enableLogicNodes: true,
        enableRenderingNodes: true
      });

      // 测试动画节点信息
      const playAnimationInfo = nodeRegistry.getNodeTypeInfo('animation/playAnimation');
      expect(playAnimationInfo).toBeDefined();
      expect(playAnimationInfo?.category).toBe('Animation');
      expect(playAnimationInfo?.description).toBe('播放指定的动画');

      // 测试输入节点信息
      const keyDownInfo = nodeRegistry.getNodeTypeInfo('input/keyDown');
      expect(keyDownInfo).toBeDefined();
      expect(keyDownInfo?.category).toBe('Input');
      expect(keyDownInfo?.description).toBe('检测按键按下事件');

      // 测试逻辑节点信息
      const comparisonInfo = nodeRegistry.getNodeTypeInfo('logic/comparison');
      expect(comparisonInfo).toBeDefined();
      expect(comparisonInfo?.category).toBe('Logic');
      expect(comparisonInfo?.description).toBe('比较两个数值');

      // 测试渲染节点信息
      const renderModeInfo = nodeRegistry.getNodeTypeInfo('rendering/setRenderMode');
      expect(renderModeInfo).toBeDefined();
      expect(renderModeInfo?.category).toBe('Rendering');
      expect(renderModeInfo?.description).toBe('设置渲染器的渲染模式');
    });
  });

  describe('节点创建测试', () => {
    beforeEach(() => {
      registerAllNodes(nodeRegistry, {
        enableAnimationNodes: true,
        enableInputNodes: true,
        enableLogicNodes: true,
        enableRenderingNodes: true
      });
    });

    test('应该能够创建动画节点实例', () => {
      const playAnimationNode = nodeRegistry.createNode('animation/playAnimation', {
        id: 'test-play-animation',
        type: 'animation/playAnimation'
      });

      expect(playAnimationNode).toBeDefined();
      expect(playAnimationNode).toBeInstanceOf(PlayAnimationNode);
      expect(playAnimationNode?.id).toBe('test-play-animation');
    });

    test('应该能够创建输入节点实例', () => {
      const keyDownNode = nodeRegistry.createNode('input/keyDown', {
        id: 'test-key-down',
        type: 'input/keyDown'
      });

      expect(keyDownNode).toBeDefined();
      expect(keyDownNode).toBeInstanceOf(KeyDownNode);
      expect(keyDownNode?.id).toBe('test-key-down');
    });

    test('应该能够创建逻辑节点实例', () => {
      const comparisonNode = nodeRegistry.createNode('logic/comparison', {
        id: 'test-comparison',
        type: 'logic/comparison'
      });

      expect(comparisonNode).toBeDefined();
      expect(comparisonNode).toBeInstanceOf(ComparisonNode);
      expect(comparisonNode?.id).toBe('test-comparison');
    });

    test('应该能够创建渲染节点实例', () => {
      const renderModeNode = nodeRegistry.createNode('rendering/setRenderMode', {
        id: 'test-render-mode',
        type: 'rendering/setRenderMode'
      });

      expect(renderModeNode).toBeDefined();
      expect(renderModeNode).toBeInstanceOf(SetRenderModeNode);
      expect(renderModeNode?.id).toBe('test-render-mode');
    });
  });

  describe('节点功能测试', () => {
    beforeEach(() => {
      registerAllNodes(nodeRegistry, {
        enableAnimationNodes: true,
        enableInputNodes: true,
        enableLogicNodes: true,
        enableRenderingNodes: true
      });
    });

    test('比较节点应该正确执行数值比较', () => {
      const comparisonNode = nodeRegistry.createNode('logic/comparison', {
        id: 'test-comparison',
        type: 'logic/comparison'
      }) as ComparisonNode;

      expect(comparisonNode).toBeDefined();

      // 设置输入值
      comparisonNode.setInputValue('valueA', 10);
      comparisonNode.setInputValue('valueB', 5);
      comparisonNode.setInputValue('operator', '>');

      // 执行节点
      const result = comparisonNode.execute();

      // 验证结果
      expect(result).toBeDefined();
      expect(result.result).toBe(true);
      expect(result.difference).toBe(5);
    });

    test('逻辑运算节点应该正确执行逻辑运算', () => {
      const logicalNode = nodeRegistry.createNode('logic/logicalOperation', {
        id: 'test-logical',
        type: 'logic/logicalOperation'
      }) as LogicalOperationNode;

      expect(logicalNode).toBeDefined();

      // 设置输入值
      logicalNode.setInputValue('valueA', true);
      logicalNode.setInputValue('valueB', false);
      logicalNode.setInputValue('operation', 'AND');

      // 执行节点
      const result = logicalNode.execute();

      // 验证结果
      expect(result).toBeDefined();
      expect(result.result).toBe(false);
    });
  });

  describe('节点统计测试', () => {
    test('应该正确统计注册的节点数量', () => {
      registerAllNodes(nodeRegistry, {
        enableAnimationNodes: true,
        enableInputNodes: true,
        enableLogicNodes: true,
        enableRenderingNodes: true
      });

      const allNodeTypes = nodeRegistry.getAllNodeTypes();
      
      // 验证至少包含我们新添加的节点
      const animationNodes = allNodeTypes.filter(info => info.category === 'Animation');
      const inputNodes = allNodeTypes.filter(info => info.category === 'Input');
      const logicNodes = allNodeTypes.filter(info => info.category === 'Logic');
      const renderingNodes = allNodeTypes.filter(info => info.category === 'Rendering');

      expect(animationNodes.length).toBeGreaterThan(0);
      expect(inputNodes.length).toBeGreaterThan(0);
      expect(logicNodes.length).toBeGreaterThan(0);
      expect(renderingNodes.length).toBeGreaterThan(0);

      console.log(`已注册节点统计:`);
      console.log(`- 动画节点: ${animationNodes.length}个`);
      console.log(`- 输入节点: ${inputNodes.length}个`);
      console.log(`- 逻辑节点: ${logicNodes.length}个`);
      console.log(`- 渲染节点: ${renderingNodes.length}个`);
      console.log(`- 总计: ${allNodeTypes.length}个节点`);
    });
  });
});
