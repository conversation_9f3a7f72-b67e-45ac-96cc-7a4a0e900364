/**
 * 资源管理节点预设
 * 提供各种资源管理和控制节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { EventNode } from '../nodes/EventNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 资源加载节点
 */
export class LoadResourceNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed', 'progress']
    });

    this.metadata.name = '加载资源';
    this.metadata.description = '加载指定的资源';
    this.metadata.category = 'Resource';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'resourcePath',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '资源路径',
      required: true
    });

    this.addInput({
      name: 'resourceType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '资源类型 (texture, model, audio, json)',
      required: true
    });

    this.addInput({
      name: 'loadOptions',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '加载选项'
    });

    // 输出插槽
    this.addOutput({
      name: 'resource',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '加载的资源'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '加载进度 (0-1)'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const resourcePath = inputs.resourcePath;
      const resourceType = inputs.resourceType;
      const loadOptions = inputs.loadOptions || {};

      if (!resourcePath || !resourceType) {
        console.error('LoadResourceNode: 缺少资源路径或类型');
        return 'failed';
      }

      // 异步加载资源
      this.loadResourceAsync(resourcePath, resourceType, loadOptions)
        .then((resource: any) => {
          this.setOutputValue('resource', resource);
          this.setOutputValue('progress', 1.0);
          return 'success';
        })
        .catch((error: any) => {
          console.error('LoadResourceNode: 资源加载失败', error);
          return 'failed';
        });

      return 'progress';
    } catch (error) {
      console.error('LoadResourceNode: 执行错误', error);
      return 'failed';
    }
  }

  private async loadResourceAsync(path: string, type: string, options: any): Promise<any> {
    return new Promise((resolve, reject) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += 0.1;
        this.setOutputValue('progress', progress);
        
        if (progress >= 1.0) {
          clearInterval(interval);
          
          // 模拟不同类型的资源加载
          const resource = {
            path: path,
            type: type,
            data: this.createMockResourceData(type),
            loaded: true,
            timestamp: Date.now(),
            options: options
          };
          
          // 添加到资源缓存
          this.addToCache(path, resource);
          
          resolve(resource);
        }
      }, 50);
    });
  }

  private createMockResourceData(type: string): any {
    switch (type) {
      case 'texture':
        return {
          width: 512,
          height: 512,
          format: 'RGBA',
          data: new Uint8Array(512 * 512 * 4)
        };
      case 'model':
        return {
          vertices: [],
          indices: [],
          materials: [],
          animations: []
        };
      case 'audio':
        return {
          duration: 10.0,
          sampleRate: 44100,
          channels: 2,
          buffer: new ArrayBuffer(0)
        };
      case 'json':
        return {};
      default:
        return null;
    }
  }

  private addToCache(path: string, resource: any): void {
    if (typeof window !== 'undefined') {
      if (!(window as any).resourceCache) {
        (window as any).resourceCache = new Map();
      }
      (window as any).resourceCache.set(path, resource);
    }
  }
}

/**
 * 资源缓存节点
 */
export class ResourceCacheNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '资源缓存';
    this.metadata.description = '管理资源缓存';
    this.metadata.category = 'Resource';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型 (get, set, remove, clear, list)',
      required: true
    });

    this.addInput({
      name: 'resourcePath',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '资源路径'
    });

    this.addInput({
      name: 'resource',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '资源对象'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '操作结果'
    });

    this.addOutput({
      name: 'cacheInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '缓存信息'
    });
  }

  public execute(): any {
    try {
      const operation = this.getInputValue('operation');
      const resourcePath = this.getInputValue('resourcePath');
      const resource = this.getInputValue('resource');

      if (!operation) {
        console.error('ResourceCacheNode: 缺少操作类型');
        return null;
      }

      const cache = this.getResourceCache();
      let result: any = null;

      switch (operation) {
        case 'get':
          result = cache.get(resourcePath);
          break;
        case 'set':
          if (resourcePath && resource) {
            cache.set(resourcePath, resource);
            result = true;
          }
          break;
        case 'remove':
          result = cache.delete(resourcePath);
          break;
        case 'clear':
          cache.clear();
          result = true;
          break;
        case 'list':
          result = Array.from(cache.keys());
          break;
        default:
          console.error(`ResourceCacheNode: 不支持的操作 ${operation}`);
          return null;
      }

      const cacheInfo = {
        size: cache.size,
        memoryUsage: this.calculateMemoryUsage(cache)
      };

      this.setOutputValue('result', result);
      this.setOutputValue('cacheInfo', cacheInfo);

      return {
        result,
        cacheInfo
      };
    } catch (error) {
      console.error('ResourceCacheNode: 执行错误', error);
      return null;
    }
  }

  private getResourceCache(): Map<string, any> {
    if (typeof window !== 'undefined') {
      if (!(window as any).resourceCache) {
        (window as any).resourceCache = new Map();
      }
      return (window as any).resourceCache;
    }
    return new Map();
  }

  private calculateMemoryUsage(cache: Map<string, any>): number {
    let totalSize = 0;
    cache.forEach((resource) => {
      if (resource.data) {
        if (resource.data instanceof ArrayBuffer) {
          totalSize += resource.data.byteLength;
        } else if (resource.data instanceof Uint8Array) {
          totalSize += resource.data.byteLength;
        } else {
          // 估算对象大小
          totalSize += JSON.stringify(resource.data).length * 2;
        }
      }
    });
    return totalSize;
  }
}

/**
 * 资源释放节点
 */
export class ReleaseResourceNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '释放资源';
    this.metadata.description = '释放指定资源';
    this.metadata.category = 'Resource';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'resource',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '要释放的资源',
      required: true
    });

    this.addInput({
      name: 'removeFromCache',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否从缓存中移除',
      defaultValue: true
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const resource = inputs.resource;
      const removeFromCache = inputs.removeFromCache !== false;

      if (!resource) {
        console.error('ReleaseResourceNode: 缺少资源');
        return 'failed';
      }

      // 释放资源数据
      if (resource.dispose && typeof resource.dispose === 'function') {
        resource.dispose();
      }

      // 清理数据引用
      if (resource.data) {
        resource.data = null;
      }

      // 从缓存中移除
      if (removeFromCache && resource.path) {
        const cache = this.getResourceCache();
        cache.delete(resource.path);
      }

      // 标记为已释放
      resource.disposed = true;

      return 'success';
    } catch (error) {
      console.error('ReleaseResourceNode: 执行错误', error);
      return 'failed';
    }
  }

  private getResourceCache(): Map<string, any> {
    if (typeof window !== 'undefined') {
      if (!(window as any).resourceCache) {
        (window as any).resourceCache = new Map();
      }
      return (window as any).resourceCache;
    }
    return new Map();
  }
}

/**
 * 纹理管理节点
 */
export class TextureManagerNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '纹理管理';
    this.metadata.description = '管理纹理资源';
    this.metadata.category = 'Resource';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型 (create, resize, filter, format)',
      required: true
    });

    this.addInput({
      name: 'texture',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '纹理对象'
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '宽度'
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '高度'
    });

    this.addInput({
      name: 'format',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '纹理格式'
    });

    this.addInput({
      name: 'filterMode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '过滤模式'
    });

    // 输出插槽
    this.addOutput({
      name: 'processedTexture',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '处理后的纹理'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const operation = inputs.operation;
      const texture = inputs.texture;
      const width = inputs.width;
      const height = inputs.height;
      const format = inputs.format;
      const filterMode = inputs.filterMode;

      if (!operation) {
        console.error('TextureManagerNode: 缺少操作类型');
        return 'failed';
      }

      let processedTexture = texture;

      switch (operation) {
        case 'create':
          processedTexture = this.createTexture(width || 256, height || 256, format || 'RGBA');
          break;
        case 'resize':
          if (texture && width && height) {
            processedTexture = this.resizeTexture(texture, width, height);
          }
          break;
        case 'filter':
          if (texture && filterMode) {
            processedTexture = this.setTextureFilter(texture, filterMode);
          }
          break;
        case 'format':
          if (texture && format) {
            processedTexture = this.convertTextureFormat(texture, format);
          }
          break;
        default:
          console.error(`TextureManagerNode: 不支持的操作 ${operation}`);
          return 'failed';
      }

      this.setOutputValue('processedTexture', processedTexture);
      return 'success';
    } catch (error) {
      console.error('TextureManagerNode: 执行错误', error);
      return 'failed';
    }
  }

  private createTexture(width: number, height: number, format: string): any {
    return {
      type: 'texture',
      width: width,
      height: height,
      format: format,
      data: new Uint8Array(width * height * 4),
      created: Date.now(),

      dispose: () => {
        console.log('Disposing texture');
      }
    };
  }

  private resizeTexture(texture: any, newWidth: number, newHeight: number): any {
    const resized = { ...texture };
    resized.width = newWidth;
    resized.height = newHeight;
    resized.data = new Uint8Array(newWidth * newHeight * 4);
    return resized;
  }

  private setTextureFilter(texture: any, filterMode: string): any {
    const filtered = { ...texture };
    filtered.filterMode = filterMode;
    return filtered;
  }

  private convertTextureFormat(texture: any, newFormat: string): any {
    const converted = { ...texture };
    converted.format = newFormat;
    return converted;
  }
}

/**
 * 模型管理节点
 */
export class ModelManagerNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '模型管理';
    this.metadata.description = '管理3D模型资源';
    this.metadata.category = 'Resource';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型 (optimize, lod, material, animation)',
      required: true
    });

    this.addInput({
      name: 'model',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '模型对象',
      required: true
    });

    this.addInput({
      name: 'parameters',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '操作参数'
    });

    // 输出插槽
    this.addOutput({
      name: 'processedModel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '处理后的模型'
    });

    this.addOutput({
      name: 'statistics',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '模型统计信息'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const operation = inputs.operation;
      const model = inputs.model;
      const parameters = inputs.parameters || {};

      if (!operation || !model) {
        console.error('ModelManagerNode: 缺少操作类型或模型');
        return 'failed';
      }

      let processedModel = model;
      let statistics = this.getModelStatistics(model);

      switch (operation) {
        case 'optimize':
          processedModel = this.optimizeModel(model, parameters);
          break;
        case 'lod':
          processedModel = this.generateLOD(model, parameters);
          break;
        case 'material':
          processedModel = this.updateMaterials(model, parameters);
          break;
        case 'animation':
          processedModel = this.processAnimations(model, parameters);
          break;
        default:
          console.error(`ModelManagerNode: 不支持的操作 ${operation}`);
          return 'failed';
      }

      statistics = this.getModelStatistics(processedModel);

      this.setOutputValue('processedModel', processedModel);
      this.setOutputValue('statistics', statistics);
      return 'success';
    } catch (error) {
      console.error('ModelManagerNode: 执行错误', error);
      return 'failed';
    }
  }

  private getModelStatistics(model: any): any {
    return {
      vertices: model.vertices?.length || 0,
      triangles: model.indices?.length / 3 || 0,
      materials: model.materials?.length || 0,
      animations: model.animations?.length || 0,
      memoryUsage: this.calculateModelMemoryUsage(model)
    };
  }

  private optimizeModel(model: any, parameters: any): any {
    const optimized = { ...model };
    // 模拟模型优化
    console.log('Optimizing model with parameters:', parameters);
    return optimized;
  }

  private generateLOD(model: any, parameters: any): any {
    const lodModel = { ...model };
    lodModel.lodLevels = parameters.levels || 3;
    return lodModel;
  }

  private updateMaterials(model: any, parameters: any): any {
    const updated = { ...model };
    updated.materials = parameters.materials || model.materials;
    return updated;
  }

  private processAnimations(model: any, parameters: any): any {
    const processed = { ...model };
    processed.animations = parameters.animations || model.animations;
    return processed;
  }

  private calculateModelMemoryUsage(model: any): number {
    let size = 0;
    if (model.vertices) size += model.vertices.length * 4;
    if (model.indices) size += model.indices.length * 4;
    return size;
  }
}

/**
 * 音频管理节点
 */
export class AudioManagerNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '音频管理';
    this.metadata.description = '管理音频资源';
    this.metadata.category = 'Resource';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型 (decode, encode, filter, analyze)',
      required: true
    });

    this.addInput({
      name: 'audioData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '音频数据',
      required: true
    });

    this.addInput({
      name: 'parameters',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '处理参数'
    });

    // 输出插槽
    this.addOutput({
      name: 'processedAudio',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '处理后的音频'
    });

    this.addOutput({
      name: 'audioInfo',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '音频信息'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const operation = inputs.operation;
      const audioData = inputs.audioData;
      const parameters = inputs.parameters || {};

      if (!operation || !audioData) {
        console.error('AudioManagerNode: 缺少操作类型或音频数据');
        return 'failed';
      }

      let processedAudio = audioData;
      let audioInfo = this.getAudioInfo(audioData);

      switch (operation) {
        case 'decode':
          processedAudio = this.decodeAudio(audioData, parameters);
          break;
        case 'encode':
          processedAudio = this.encodeAudio(audioData, parameters);
          break;
        case 'filter':
          processedAudio = this.filterAudio(audioData, parameters);
          break;
        case 'analyze':
          audioInfo = this.analyzeAudio(audioData);
          break;
        default:
          console.error(`AudioManagerNode: 不支持的操作 ${operation}`);
          return 'failed';
      }

      audioInfo = this.getAudioInfo(processedAudio);

      this.setOutputValue('processedAudio', processedAudio);
      this.setOutputValue('audioInfo', audioInfo);
      return 'success';
    } catch (error) {
      console.error('AudioManagerNode: 执行错误', error);
      return 'failed';
    }
  }

  private getAudioInfo(audioData: any): any {
    return {
      duration: audioData.duration || 0,
      sampleRate: audioData.sampleRate || 44100,
      channels: audioData.channels || 2,
      bitRate: audioData.bitRate || 128,
      format: audioData.format || 'PCM',
      size: audioData.buffer?.byteLength || 0
    };
  }

  private decodeAudio(audioData: any, parameters: any): any {
    const decoded = { ...audioData };
    decoded.format = 'PCM';
    decoded.decoded = true;
    return decoded;
  }

  private encodeAudio(audioData: any, parameters: any): any {
    const encoded = { ...audioData };
    encoded.format = parameters.format || 'MP3';
    encoded.bitRate = parameters.bitRate || 128;
    encoded.encoded = true;
    return encoded;
  }

  private filterAudio(audioData: any, parameters: any): any {
    const filtered = { ...audioData };
    filtered.filters = parameters.filters || [];
    filtered.filtered = true;
    return filtered;
  }

  private analyzeAudio(audioData: any): any {
    return {
      ...this.getAudioInfo(audioData),
      spectrum: this.generateMockSpectrum(),
      peaks: this.findAudioPeaks(audioData),
      rms: this.calculateRMS(audioData)
    };
  }

  private generateMockSpectrum(): number[] {
    return Array.from({ length: 256 }, () => Math.random());
  }

  private findAudioPeaks(audioData: any): number[] {
    // 模拟峰值检测
    return [0.1, 0.3, 0.7, 0.9];
  }

  private calculateRMS(audioData: any): number {
    // 模拟RMS计算
    return 0.5;
  }
}

/**
 * 注册资源管理节点到节点注册表
 */
export function registerResourceNodes(registry: any): void {
  // 基础资源管理节点
  registry.registerNodeType({
    type: 'resource/loadResource',
    category: 'Resource',
    description: '加载指定的资源',
    constructor: LoadResourceNode,
    icon: '📁',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'resource/resourceCache',
    category: 'Resource',
    description: '管理资源缓存',
    constructor: ResourceCacheNode,
    icon: '💾',
    color: '#2196F3'
  });

  registry.registerNodeType({
    type: 'resource/releaseResource',
    category: 'Resource',
    description: '释放指定资源',
    constructor: ReleaseResourceNode,
    icon: '🗑️',
    color: '#F44336'
  });

  // 专用资源管理节点
  registry.registerNodeType({
    type: 'resource/textureManager',
    category: 'Resource',
    description: '管理纹理资源',
    constructor: TextureManagerNode,
    icon: '🖼️',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'resource/modelManager',
    category: 'Resource',
    description: '管理3D模型资源',
    constructor: ModelManagerNode,
    icon: '🎭',
    color: '#9C27B0'
  });

  registry.registerNodeType({
    type: 'resource/audioManager',
    category: 'Resource',
    description: '管理音频资源',
    constructor: AudioManagerNode,
    icon: '🎵',
    color: '#3F51B5'
  });
}
