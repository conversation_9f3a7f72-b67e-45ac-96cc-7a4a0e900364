/**
 * 渲染节点预设
 * 提供各种渲染控制和处理节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 设置渲染模式节点
 */
export class SetRenderModeNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置渲染模式';
    this.metadata.description = '设置渲染器的渲染模式';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'renderMode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '渲染模式 (wireframe, solid, points)',
      defaultValue: 'solid'
    });

    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const renderMode = inputs.renderMode || 'solid';
      const renderer = inputs.renderer;

      // 如果没有提供渲染器，尝试获取全局渲染器
      const targetRenderer = renderer || this.getGlobalRenderer();

      if (!targetRenderer) {
        console.error('SetRenderModeNode: 无法获取渲染器');
        return 'failed';
      }

      // 设置渲染模式
      if (targetRenderer.setRenderMode && typeof targetRenderer.setRenderMode === 'function') {
        targetRenderer.setRenderMode(renderMode);
      } else if (targetRenderer.renderMode !== undefined) {
        targetRenderer.renderMode = renderMode;
      } else {
        console.error('SetRenderModeNode: 渲染器不支持设置渲染模式');
        return 'failed';
      }

      return 'success';
    } catch (error) {
      console.error('SetRenderModeNode: 执行错误', error);
      return 'failed';
    }
  }

  private getGlobalRenderer(): any {
    // 尝试从全局上下文获取渲染器
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}

/**
 * 设置相机模式节点
 */
export class SetCameraModeNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置相机模式';
    this.metadata.description = '设置相机的投影模式';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '相机实例',
      required: true
    });

    this.addInput({
      name: 'mode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '投影模式 (perspective, orthographic)',
      defaultValue: 'perspective'
    });

    this.addInput({
      name: 'fov',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '视野角度（透视模式）',
      defaultValue: 75
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '正交尺寸（正交模式）',
      defaultValue: 10
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const camera = inputs.camera;
      const mode = inputs.mode || 'perspective';
      const fov = inputs.fov || 75;
      const size = inputs.size || 10;

      if (!camera) {
        console.error('SetCameraModeNode: 缺少相机实例');
        return 'failed';
      }

      if (mode === 'perspective') {
        if (camera.setPerspective && typeof camera.setPerspective === 'function') {
          camera.setPerspective(fov);
        } else if (camera.fov !== undefined) {
          camera.fov = fov;
          camera.updateProjectionMatrix && camera.updateProjectionMatrix();
        } else {
          console.error('SetCameraModeNode: 相机不支持透视模式');
          return 'failed';
        }
      } else if (mode === 'orthographic') {
        if (camera.setOrthographic && typeof camera.setOrthographic === 'function') {
          camera.setOrthographic(size);
        } else if (camera.left !== undefined) {
          camera.left = -size;
          camera.right = size;
          camera.top = size;
          camera.bottom = -size;
          camera.updateProjectionMatrix && camera.updateProjectionMatrix();
        } else {
          console.error('SetCameraModeNode: 相机不支持正交模式');
          return 'failed';
        }
      } else {
        console.error(`SetCameraModeNode: 不支持的相机模式 ${mode}`);
        return 'failed';
      }

      return 'success';
    } catch (error) {
      console.error('SetCameraModeNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 设置视口节点
 */
export class SetViewportNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置视口';
    this.metadata.description = '设置渲染视口';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'x',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'X坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'y',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Y坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '宽度',
      defaultValue: 800
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '高度',
      defaultValue: 600
    });

    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const x = inputs.x || 0;
      const y = inputs.y || 0;
      const width = inputs.width || 800;
      const height = inputs.height || 600;
      const renderer = inputs.renderer;

      // 如果没有提供渲染器，尝试获取全局渲染器
      const targetRenderer = renderer || this.getGlobalRenderer();

      if (!targetRenderer) {
        console.error('SetViewportNode: 无法获取渲染器');
        return 'failed';
      }

      // 设置视口
      if (targetRenderer.setViewport && typeof targetRenderer.setViewport === 'function') {
        targetRenderer.setViewport(x, y, width, height);
      } else if (targetRenderer.setSize && typeof targetRenderer.setSize === 'function') {
        targetRenderer.setSize(width, height);
      } else {
        console.error('SetViewportNode: 渲染器不支持设置视口');
        return 'failed';
      }

      return 'success';
    } catch (error) {
      console.error('SetViewportNode: 执行错误', error);
      return 'failed';
    }
  }

  private getGlobalRenderer(): any {
    // 尝试从全局上下文获取渲染器
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}

/**
 * 设置渲染质量节点
 */
export class SetRenderQualityNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置渲染质量';
    this.metadata.description = '设置渲染质量级别';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'qualityLevel',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '质量级别 (low, medium, high, ultra)',
      defaultValue: 'medium'
    });

    this.addInput({
      name: 'customSettings',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '自定义设置'
    });

    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const qualityLevel = inputs.qualityLevel || 'medium';
      const customSettings = inputs.customSettings || {};
      const renderer = inputs.renderer;

      // 如果没有提供渲染器，尝试获取全局渲染器
      const targetRenderer = renderer || this.getGlobalRenderer();

      if (!targetRenderer) {
        console.error('SetRenderQualityNode: 无法获取渲染器');
        return 'failed';
      }

      // 预定义质量设置
      const qualitySettings = {
        low: {
          shadowMapSize: 512,
          antialias: false,
          pixelRatio: 0.5,
          maxLights: 4,
          shadowCascades: 1
        },
        medium: {
          shadowMapSize: 1024,
          antialias: true,
          pixelRatio: 1.0,
          maxLights: 8,
          shadowCascades: 2
        },
        high: {
          shadowMapSize: 2048,
          antialias: true,
          pixelRatio: 1.0,
          maxLights: 16,
          shadowCascades: 3
        },
        ultra: {
          shadowMapSize: 4096,
          antialias: true,
          pixelRatio: window.devicePixelRatio || 1.0,
          maxLights: 32,
          shadowCascades: 4
        }
      };

      const settings = { ...qualitySettings[qualityLevel as keyof typeof qualitySettings], ...customSettings };

      // 应用设置到渲染器
      if (targetRenderer.setPixelRatio && settings.pixelRatio) {
        targetRenderer.setPixelRatio(settings.pixelRatio);
      }

      if (targetRenderer.shadowMap && settings.shadowMapSize) {
        targetRenderer.shadowMap.mapSize.width = settings.shadowMapSize;
        targetRenderer.shadowMap.mapSize.height = settings.shadowMapSize;
      }

      // 应用其他设置
      Object.keys(settings).forEach(key => {
        if (targetRenderer[key] !== undefined) {
          targetRenderer[key] = settings[key];
        }
      });

      return 'success';
    } catch (error) {
      console.error('SetRenderQualityNode: 执行错误', error);
      return 'failed';
    }
  }

  private getGlobalRenderer(): any {
    // 尝试从全局上下文获取渲染器
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}

/**
 * 获取渲染统计节点
 */
export class GetRenderStatsNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '获取渲染统计';
    this.metadata.description = '获取渲染性能统计信息';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });

    // 输出插槽
    this.addOutput({
      name: 'fps',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '帧率'
    });

    this.addOutput({
      name: 'triangles',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '三角形数量'
    });

    this.addOutput({
      name: 'drawCalls',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '绘制调用数'
    });

    this.addOutput({
      name: 'memoryUsage',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '内存使用情况'
    });
  }

  public execute(): any {
    try {
      const renderer = this.getInputValue('renderer') || this.getGlobalRenderer();

      if (!renderer) {
        console.error('GetRenderStatsNode: 无法获取渲染器');
        return null;
      }

      // 获取渲染统计信息
      const info = renderer.info || {};
      const memory = info.memory || {};
      const render = info.render || {};

      const fps = this.calculateFPS();
      const triangles = render.triangles || 0;
      const drawCalls = render.calls || 0;
      const memoryUsage = {
        geometries: memory.geometries || 0,
        textures: memory.textures || 0,
        programs: info.programs?.length || 0
      };

      this.setOutputValue('fps', fps);
      this.setOutputValue('triangles', triangles);
      this.setOutputValue('drawCalls', drawCalls);
      this.setOutputValue('memoryUsage', memoryUsage);

      return {
        fps,
        triangles,
        drawCalls,
        memoryUsage
      };
    } catch (error) {
      console.error('GetRenderStatsNode: 执行错误', error);
      return null;
    }
  }

  private calculateFPS(): number {
    // 简化的FPS计算
    const now = performance.now();
    if (!this.lastTime) {
      this.lastTime = now;
      this.frameCount = 0;
      return 0;
    }

    this.frameCount++;
    const elapsed = now - this.lastTime;

    if (elapsed >= 1000) { // 每秒更新一次
      const fps = Math.round((this.frameCount * 1000) / elapsed);
      this.lastTime = now;
      this.frameCount = 0;
      return fps;
    }

    return this.lastFPS || 0;
  }

  private lastTime?: number;
  private frameCount: number = 0;
  private lastFPS?: number;

  private getGlobalRenderer(): any {
    // 尝试从全局上下文获取渲染器
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}

/**
 * 材质控制节点
 */
export class MaterialControlNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '材质控制';
    this.metadata.description = '控制材质属性';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'material',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '材质实例',
      required: true
    });

    this.addInput({
      name: 'property',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '属性名称',
      required: true
    });

    this.addInput({
      name: 'value',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '属性值',
      required: true
    });

    // 输出插槽
    this.addOutput({
      name: 'updatedMaterial',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '更新后的材质'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const material = inputs.material;
      const property = inputs.property;
      const value = inputs.value;

      if (!material || !property) {
        console.error('MaterialControlNode: 缺少材质或属性名称');
        return 'failed';
      }

      // 设置材质属性
      if (material[property] !== undefined) {
        material[property] = value;

        // 如果材质有needsUpdate属性，标记需要更新
        if (material.needsUpdate !== undefined) {
          material.needsUpdate = true;
        }

        this.setOutputValue('updatedMaterial', material);
        return 'success';
      } else {
        console.error(`MaterialControlNode: 材质不存在属性 ${property}`);
        return 'failed';
      }
    } catch (error) {
      console.error('MaterialControlNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 光照控制节点
 */
export class LightControlNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '光照控制';
    this.metadata.description = '控制光源属性';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'light',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '光源实例',
      required: true
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '光照强度'
    });

    this.addInput({
      name: 'color',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '光照颜色'
    });

    this.addInput({
      name: 'position',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '光源位置'
    });

    this.addInput({
      name: 'castShadow',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否投射阴影'
    });

    // 输出插槽
    this.addOutput({
      name: 'updatedLight',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '更新后的光源'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const light = inputs.light;

      if (!light) {
        console.error('LightControlNode: 缺少光源实例');
        return 'failed';
      }

      // 更新光照强度
      if (inputs.intensity !== undefined && light.intensity !== undefined) {
        light.intensity = inputs.intensity;
      }

      // 更新光照颜色
      if (inputs.color && light.color) {
        if (light.color.setHex && typeof inputs.color === 'number') {
          light.color.setHex(inputs.color);
        } else if (light.color.set) {
          light.color.set(inputs.color);
        }
      }

      // 更新光源位置
      if (inputs.position && light.position) {
        if (inputs.position.x !== undefined) light.position.x = inputs.position.x;
        if (inputs.position.y !== undefined) light.position.y = inputs.position.y;
        if (inputs.position.z !== undefined) light.position.z = inputs.position.z;
      }

      // 更新阴影设置
      if (inputs.castShadow !== undefined && light.castShadow !== undefined) {
        light.castShadow = inputs.castShadow;
      }

      this.setOutputValue('updatedLight', light);
      return 'success';
    } catch (error) {
      console.error('LightControlNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 阴影控制节点
 */
export class ShadowControlNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '阴影控制';
    this.metadata.description = '控制阴影设置';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });

    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '启用阴影',
      defaultValue: true
    });

    this.addInput({
      name: 'shadowType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '阴影类型 (basic, pcf, pcfsoft)',
      defaultValue: 'pcf'
    });

    this.addInput({
      name: 'shadowMapSize',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '阴影贴图尺寸',
      defaultValue: 1024
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const renderer = inputs.renderer || this.getGlobalRenderer();
      const enabled = inputs.enabled;
      const shadowType = inputs.shadowType || 'pcf';
      const shadowMapSize = inputs.shadowMapSize || 1024;

      if (!renderer) {
        console.error('ShadowControlNode: 无法获取渲染器');
        return 'failed';
      }

      // 启用/禁用阴影
      if (enabled !== undefined) {
        renderer.shadowMap.enabled = enabled;
      }

      // 设置阴影类型
      if (renderer.shadowMap.type !== undefined) {
        const shadowTypes: { [key: string]: any } = {
          'basic': 0, // BasicShadowMap
          'pcf': 1,   // PCFShadowMap
          'pcfsoft': 2 // PCFSoftShadowMap
        };

        if (shadowTypes[shadowType] !== undefined) {
          renderer.shadowMap.type = shadowTypes[shadowType];
        }
      }

      // 设置阴影贴图尺寸
      if (renderer.shadowMap.mapSize) {
        renderer.shadowMap.mapSize.width = shadowMapSize;
        renderer.shadowMap.mapSize.height = shadowMapSize;
      }

      return 'success';
    } catch (error) {
      console.error('ShadowControlNode: 执行错误', error);
      return 'failed';
    }
  }

  private getGlobalRenderer(): any {
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}

/**
 * 后处理效果节点
 */
export class PostProcessingNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '后处理效果';
    this.metadata.description = '应用后处理效果';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'effectType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '效果类型 (bloom, blur, sepia, vignette)',
      required: true
    });

    this.addInput({
      name: 'intensity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '效果强度 (0-1)',
      defaultValue: 0.5
    });

    this.addInput({
      name: 'parameters',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '效果参数'
    });

    this.addInput({
      name: 'composer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '后处理合成器'
    });

    // 输出插槽
    this.addOutput({
      name: 'effectInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '效果实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const effectType = inputs.effectType;
      const intensity = Math.max(0, Math.min(1, inputs.intensity || 0.5));
      const parameters = inputs.parameters || {};
      const composer = inputs.composer;

      if (!effectType) {
        console.error('PostProcessingNode: 缺少效果类型');
        return 'failed';
      }

      // 创建效果实例
      const effectInstance = {
        type: effectType,
        intensity: intensity,
        parameters: { ...parameters },
        enabled: true,

        apply: (scene: any, camera: any, renderTarget: any) => {
          // 这里应该实现具体的后处理效果
          // 简化实现，实际需要根据具体的渲染引擎来实现
          console.log(`Applying ${effectType} effect with intensity ${intensity}`);
          return renderTarget;
        },

        setIntensity: (newIntensity: number) => {
          effectInstance.intensity = Math.max(0, Math.min(1, newIntensity));
        },

        setParameter: (key: string, value: any) => {
          effectInstance.parameters[key] = value;
        },

        enable: () => {
          effectInstance.enabled = true;
        },

        disable: () => {
          effectInstance.enabled = false;
        }
      };

      // 如果提供了合成器，添加到合成器中
      if (composer && composer.addPass) {
        composer.addPass(effectInstance);
      }

      this.setOutputValue('effectInstance', effectInstance);
      return 'success';
    } catch (error) {
      console.error('PostProcessingNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 注册渲染节点到节点注册表
 */
export function registerRenderingNodes(registry: any): void {
  // 基础渲染节点
  registry.registerNodeType({
    type: 'rendering/setRenderMode',
    category: 'Rendering',
    description: '设置渲染器的渲染模式',
    constructor: SetRenderModeNode,
    icon: '🎨',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'rendering/setCameraMode',
    category: 'Rendering',
    description: '设置相机的投影模式',
    constructor: SetCameraModeNode,
    icon: '📷',
    color: '#2196F3'
  });

  registry.registerNodeType({
    type: 'rendering/setViewport',
    category: 'Rendering',
    description: '设置渲染视口',
    constructor: SetViewportNode,
    icon: '🖼️',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'rendering/screenshot',
    category: 'Rendering',
    description: '截取当前渲染画面',
    constructor: ScreenshotNode,
    icon: '📸',
    color: '#9C27B0'
  });

  registry.registerNodeType({
    type: 'rendering/setRenderQuality',
    category: 'Rendering',
    description: '设置渲染质量',
    constructor: SetRenderQualityNode,
    icon: '⚙️',
    color: '#607D8B'
  });

  registry.registerNodeType({
    type: 'rendering/getRenderStats',
    category: 'Rendering',
    description: '获取渲染统计信息',
    constructor: GetRenderStatsNode,
    icon: '📊',
    color: '#795548'
  });

  registry.registerNodeType({
    type: 'rendering/materialControl',
    category: 'Rendering',
    description: '材质控制',
    constructor: MaterialControlNode,
    icon: '🎨',
    color: '#E91E63'
  });

  registry.registerNodeType({
    type: 'rendering/lightControl',
    category: 'Rendering',
    description: '光照控制',
    constructor: LightControlNode,
    icon: '💡',
    color: '#FFC107'
  });

  registry.registerNodeType({
    type: 'rendering/shadowControl',
    category: 'Rendering',
    description: '阴影控制',
    constructor: ShadowControlNode,
    icon: '🌑',
    color: '#424242'
  });

  registry.registerNodeType({
    type: 'rendering/postProcessing',
    category: 'Rendering',
    description: '后处理效果',
    constructor: PostProcessingNode,
    icon: '✨',
    color: '#9C27B0'
  });
}

/**
 * 截图节点
 */
export class ScreenshotNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '截图';
    this.metadata.description = '截取当前渲染画面';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'format',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '图像格式 (png, jpeg, webp)',
      defaultValue: 'png'
    });

    this.addInput({
      name: 'quality',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '图像质量 (0-1)',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });

    // 输出插槽
    this.addOutput({
      name: 'imageData',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '图像数据（Base64）'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const format = inputs.format || 'png';
      const quality = Math.max(0, Math.min(1, inputs.quality || 1.0));
      const renderer = inputs.renderer;

      // 如果没有提供渲染器，尝试获取全局渲染器
      const targetRenderer = renderer || this.getGlobalRenderer();

      if (!targetRenderer) {
        console.error('ScreenshotNode: 无法获取渲染器');
        return 'failed';
      }

      // 获取canvas元素
      let canvas: HTMLCanvasElement | null = null;
      if (targetRenderer.domElement && targetRenderer.domElement instanceof HTMLCanvasElement) {
        canvas = targetRenderer.domElement;
      } else if (targetRenderer.getCanvas && typeof targetRenderer.getCanvas === 'function') {
        canvas = targetRenderer.getCanvas();
      }

      if (!canvas) {
        console.error('ScreenshotNode: 无法获取canvas元素');
        return 'failed';
      }

      // 生成图像数据
      const mimeType = `image/${format}`;
      const imageData = canvas.toDataURL(mimeType, quality);

      this.setOutputValue('imageData', imageData);
      return 'success';
    } catch (error) {
      console.error('ScreenshotNode: 执行错误', error);
      return 'failed';
    }
  }

  private getGlobalRenderer(): any {
    // 尝试从全局上下文获取渲染器
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}
