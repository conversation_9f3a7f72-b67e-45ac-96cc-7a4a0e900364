/**
 * 渲染节点预设
 * 提供各种渲染控制和处理节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 设置渲染模式节点
 */
export class SetRenderModeNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置渲染模式';
    this.metadata.description = '设置渲染器的渲染模式';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'renderMode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '渲染模式 (wireframe, solid, points)',
      defaultValue: 'solid'
    });

    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const renderMode = inputs.renderMode || 'solid';
      const renderer = inputs.renderer;

      // 如果没有提供渲染器，尝试获取全局渲染器
      const targetRenderer = renderer || this.getGlobalRenderer();

      if (!targetRenderer) {
        console.error('SetRenderModeNode: 无法获取渲染器');
        return 'failed';
      }

      // 设置渲染模式
      if (targetRenderer.setRenderMode && typeof targetRenderer.setRenderMode === 'function') {
        targetRenderer.setRenderMode(renderMode);
      } else if (targetRenderer.renderMode !== undefined) {
        targetRenderer.renderMode = renderMode;
      } else {
        console.error('SetRenderModeNode: 渲染器不支持设置渲染模式');
        return 'failed';
      }

      return 'success';
    } catch (error) {
      console.error('SetRenderModeNode: 执行错误', error);
      return 'failed';
    }
  }

  private getGlobalRenderer(): any {
    // 尝试从全局上下文获取渲染器
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}

/**
 * 设置相机模式节点
 */
export class SetCameraModeNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置相机模式';
    this.metadata.description = '设置相机的投影模式';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'camera',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '相机实例',
      required: true
    });

    this.addInput({
      name: 'mode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '投影模式 (perspective, orthographic)',
      defaultValue: 'perspective'
    });

    this.addInput({
      name: 'fov',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '视野角度（透视模式）',
      defaultValue: 75
    });

    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '正交尺寸（正交模式）',
      defaultValue: 10
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const camera = inputs.camera;
      const mode = inputs.mode || 'perspective';
      const fov = inputs.fov || 75;
      const size = inputs.size || 10;

      if (!camera) {
        console.error('SetCameraModeNode: 缺少相机实例');
        return 'failed';
      }

      if (mode === 'perspective') {
        if (camera.setPerspective && typeof camera.setPerspective === 'function') {
          camera.setPerspective(fov);
        } else if (camera.fov !== undefined) {
          camera.fov = fov;
          camera.updateProjectionMatrix && camera.updateProjectionMatrix();
        } else {
          console.error('SetCameraModeNode: 相机不支持透视模式');
          return 'failed';
        }
      } else if (mode === 'orthographic') {
        if (camera.setOrthographic && typeof camera.setOrthographic === 'function') {
          camera.setOrthographic(size);
        } else if (camera.left !== undefined) {
          camera.left = -size;
          camera.right = size;
          camera.top = size;
          camera.bottom = -size;
          camera.updateProjectionMatrix && camera.updateProjectionMatrix();
        } else {
          console.error('SetCameraModeNode: 相机不支持正交模式');
          return 'failed';
        }
      } else {
        console.error(`SetCameraModeNode: 不支持的相机模式 ${mode}`);
        return 'failed';
      }

      return 'success';
    } catch (error) {
      console.error('SetCameraModeNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 设置视口节点
 */
export class SetViewportNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置视口';
    this.metadata.description = '设置渲染视口';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'x',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'X坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'y',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: 'Y坐标',
      defaultValue: 0
    });

    this.addInput({
      name: 'width',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '宽度',
      defaultValue: 800
    });

    this.addInput({
      name: 'height',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '高度',
      defaultValue: 600
    });

    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const x = inputs.x || 0;
      const y = inputs.y || 0;
      const width = inputs.width || 800;
      const height = inputs.height || 600;
      const renderer = inputs.renderer;

      // 如果没有提供渲染器，尝试获取全局渲染器
      const targetRenderer = renderer || this.getGlobalRenderer();

      if (!targetRenderer) {
        console.error('SetViewportNode: 无法获取渲染器');
        return 'failed';
      }

      // 设置视口
      if (targetRenderer.setViewport && typeof targetRenderer.setViewport === 'function') {
        targetRenderer.setViewport(x, y, width, height);
      } else if (targetRenderer.setSize && typeof targetRenderer.setSize === 'function') {
        targetRenderer.setSize(width, height);
      } else {
        console.error('SetViewportNode: 渲染器不支持设置视口');
        return 'failed';
      }

      return 'success';
    } catch (error) {
      console.error('SetViewportNode: 执行错误', error);
      return 'failed';
    }
  }

  private getGlobalRenderer(): any {
    // 尝试从全局上下文获取渲染器
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}

/**
 * 注册渲染节点到节点注册表
 */
export function registerRenderingNodes(registry: any): void {
  // 基础渲染节点
  registry.registerNodeType({
    type: 'rendering/setRenderMode',
    category: 'Rendering',
    description: '设置渲染器的渲染模式',
    constructor: SetRenderModeNode,
    icon: '🎨',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'rendering/setCameraMode',
    category: 'Rendering',
    description: '设置相机的投影模式',
    constructor: SetCameraModeNode,
    icon: '📷',
    color: '#2196F3'
  });

  registry.registerNodeType({
    type: 'rendering/setViewport',
    category: 'Rendering',
    description: '设置渲染视口',
    constructor: SetViewportNode,
    icon: '🖼️',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'rendering/screenshot',
    category: 'Rendering',
    description: '截取当前渲染画面',
    constructor: ScreenshotNode,
    icon: '📸',
    color: '#9C27B0'
  });
}

/**
 * 截图节点
 */
export class ScreenshotNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '截图';
    this.metadata.description = '截取当前渲染画面';
    this.metadata.category = 'Rendering';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'format',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '图像格式 (png, jpeg, webp)',
      defaultValue: 'png'
    });

    this.addInput({
      name: 'quality',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '图像质量 (0-1)',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'renderer',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '渲染器实例'
    });

    // 输出插槽
    this.addOutput({
      name: 'imageData',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '图像数据（Base64）'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const format = inputs.format || 'png';
      const quality = Math.max(0, Math.min(1, inputs.quality || 1.0));
      const renderer = inputs.renderer;

      // 如果没有提供渲染器，尝试获取全局渲染器
      const targetRenderer = renderer || this.getGlobalRenderer();

      if (!targetRenderer) {
        console.error('ScreenshotNode: 无法获取渲染器');
        return 'failed';
      }

      // 获取canvas元素
      let canvas: HTMLCanvasElement | null = null;
      if (targetRenderer.domElement && targetRenderer.domElement instanceof HTMLCanvasElement) {
        canvas = targetRenderer.domElement;
      } else if (targetRenderer.getCanvas && typeof targetRenderer.getCanvas === 'function') {
        canvas = targetRenderer.getCanvas();
      }

      if (!canvas) {
        console.error('ScreenshotNode: 无法获取canvas元素');
        return 'failed';
      }

      // 生成图像数据
      const mimeType = `image/${format}`;
      const imageData = canvas.toDataURL(mimeType, quality);

      this.setOutputValue('imageData', imageData);
      return 'success';
    } catch (error) {
      console.error('ScreenshotNode: 执行错误', error);
      return 'failed';
    }
  }

  private getGlobalRenderer(): any {
    // 尝试从全局上下文获取渲染器
    if (typeof window !== 'undefined' && (window as any).dlEngine) {
      return (window as any).dlEngine.renderer;
    }
    return null;
  }
}
