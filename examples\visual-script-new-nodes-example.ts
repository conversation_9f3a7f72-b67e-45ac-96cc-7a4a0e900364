/**
 * 新节点使用示例
 * 展示如何使用新开发的动画、输入、逻辑和渲染节点
 */

import { NodeRegistry } from '../engine/src/visualscript/nodes/NodeRegistry';
import { registerAllNodes } from '../engine/src/visualscript/presets/OptimizedNodeRegistry';
import { Graph } from '../engine/src/visualscript/graph/Graph';
import { ExecutionContext } from '../engine/src/visualscript/execution/ExecutionContext';

// 创建节点注册表
const nodeRegistry = new NodeRegistry();

// 注册所有节点，包括新开发的节点
registerAllNodes(nodeRegistry, {
  enableCoreNodes: true,
  enableAnimationNodes: true,
  enableInputNodes: true,
  enableLogicNodes: true,
  enableRenderingNodes: true,
  debugMode: true
});

console.log('🎯 视觉脚本新节点示例');
console.log(`📊 已注册 ${nodeRegistry.getAllNodeTypes().length} 个节点类型`);

// 创建图形和执行上下文
const graph = new Graph();
const executionContext = new ExecutionContext();

/**
 * 示例1：动画控制脚本
 * 演示如何使用动画节点控制角色动画
 */
function createAnimationControlExample() {
  console.log('\n🎬 示例1：动画控制脚本');

  // 创建播放动画节点
  const playAnimationNode = nodeRegistry.createNode('animation/playAnimation', {
    id: 'play-walk-animation',
    type: 'animation/playAnimation'
  });

  if (playAnimationNode) {
    // 设置动画参数
    playAnimationNode.setInputValue('animationName', 'walk');
    playAnimationNode.setInputValue('loop', true);
    playAnimationNode.setInputValue('speed', 1.0);

    console.log('✅ 创建播放动画节点成功');
    console.log(`   - 动画名称: walk`);
    console.log(`   - 循环播放: true`);
    console.log(`   - 播放速度: 1.0`);
  }

  // 创建获取动画时间节点
  const getAnimationTimeNode = nodeRegistry.createNode('animation/getAnimationTime', {
    id: 'get-animation-time',
    type: 'animation/getAnimationTime'
  });

  if (getAnimationTimeNode) {
    console.log('✅ 创建获取动画时间节点成功');
  }

  return { playAnimationNode, getAnimationTimeNode };
}

/**
 * 示例2：输入处理脚本
 * 演示如何使用输入节点处理用户输入
 */
function createInputHandlingExample() {
  console.log('\n🎮 示例2：输入处理脚本');

  // 创建按键检测节点
  const keyDownNode = nodeRegistry.createNode('input/keyDown', {
    id: 'detect-space-key',
    type: 'input/keyDown'
  });

  if (keyDownNode) {
    // 设置检测空格键
    keyDownNode.setInputValue('keyCode', 'Space');
    console.log('✅ 创建按键检测节点成功');
    console.log(`   - 检测按键: Space`);
  }

  // 创建鼠标检测节点
  const mouseDownNode = nodeRegistry.createNode('input/mouseDown', {
    id: 'detect-left-click',
    type: 'input/mouseDown'
  });

  if (mouseDownNode) {
    // 设置检测左键点击
    mouseDownNode.setInputValue('button', 0);
    console.log('✅ 创建鼠标检测节点成功');
    console.log(`   - 检测按钮: 左键(0)`);
  }

  return { keyDownNode, mouseDownNode };
}

/**
 * 示例3：逻辑判断脚本
 * 演示如何使用逻辑节点进行条件判断
 */
function createLogicExample() {
  console.log('\n🧠 示例3：逻辑判断脚本');

  // 创建数值比较节点
  const comparisonNode = nodeRegistry.createNode('logic/comparison', {
    id: 'compare-health',
    type: 'logic/comparison'
  });

  if (comparisonNode) {
    // 设置比较参数
    comparisonNode.setInputValue('valueA', 50);
    comparisonNode.setInputValue('valueB', 20);
    comparisonNode.setInputValue('operator', '>');

    // 执行比较
    const result = comparisonNode.execute();
    console.log('✅ 创建数值比较节点成功');
    console.log(`   - 比较: 50 > 20`);
    console.log(`   - 结果: ${result?.result}`);
    console.log(`   - 差值: ${result?.difference}`);
  }

  // 创建逻辑运算节点
  const logicalNode = nodeRegistry.createNode('logic/logicalOperation', {
    id: 'logical-and',
    type: 'logic/logicalOperation'
  });

  if (logicalNode) {
    // 设置逻辑运算参数
    logicalNode.setInputValue('valueA', true);
    logicalNode.setInputValue('valueB', false);
    logicalNode.setInputValue('operation', 'AND');

    // 执行逻辑运算
    const result = logicalNode.execute();
    console.log('✅ 创建逻辑运算节点成功');
    console.log(`   - 运算: true AND false`);
    console.log(`   - 结果: ${result?.result}`);
  }

  return { comparisonNode, logicalNode };
}

/**
 * 示例4：渲染控制脚本
 * 演示如何使用渲染节点控制渲染效果
 */
function createRenderingExample() {
  console.log('\n🎨 示例4：渲染控制脚本');

  // 创建设置渲染模式节点
  const setRenderModeNode = nodeRegistry.createNode('rendering/setRenderMode', {
    id: 'set-wireframe-mode',
    type: 'rendering/setRenderMode'
  });

  if (setRenderModeNode) {
    // 设置为线框模式
    setRenderModeNode.setInputValue('renderMode', 'wireframe');
    console.log('✅ 创建设置渲染模式节点成功');
    console.log(`   - 渲染模式: wireframe`);
  }

  // 创建截图节点
  const screenshotNode = nodeRegistry.createNode('rendering/screenshot', {
    id: 'take-screenshot',
    type: 'rendering/screenshot'
  });

  if (screenshotNode) {
    // 设置截图参数
    screenshotNode.setInputValue('format', 'png');
    screenshotNode.setInputValue('quality', 1.0);
    console.log('✅ 创建截图节点成功');
    console.log(`   - 格式: png`);
    console.log(`   - 质量: 1.0`);
  }

  return { setRenderModeNode, screenshotNode };
}

/**
 * 示例5：复合脚本
 * 演示如何组合多种节点创建复杂的交互逻辑
 */
function createComplexExample() {
  console.log('\n🔗 示例5：复合交互脚本');
  console.log('场景：按空格键时，如果角色血量大于50，则播放跳跃动画并截图');

  const nodes = {
    // 输入检测
    keyDown: nodeRegistry.createNode('input/keyDown', {
      id: 'space-key-input',
      type: 'input/keyDown'
    }),
    
    // 血量比较
    healthCheck: nodeRegistry.createNode('logic/comparison', {
      id: 'health-comparison',
      type: 'logic/comparison'
    }),
    
    // 播放跳跃动画
    playJump: nodeRegistry.createNode('animation/playAnimation', {
      id: 'play-jump',
      type: 'animation/playAnimation'
    }),
    
    // 截图
    screenshot: nodeRegistry.createNode('rendering/screenshot', {
      id: 'capture-jump',
      type: 'rendering/screenshot'
    })
  };

  // 配置节点参数
  if (nodes.keyDown) {
    nodes.keyDown.setInputValue('keyCode', 'Space');
  }

  if (nodes.healthCheck) {
    nodes.healthCheck.setInputValue('valueA', 75); // 当前血量
    nodes.healthCheck.setInputValue('valueB', 50); // 最低血量
    nodes.healthCheck.setInputValue('operator', '>');
  }

  if (nodes.playJump) {
    nodes.playJump.setInputValue('animationName', 'jump');
    nodes.playJump.setInputValue('loop', false);
    nodes.playJump.setInputValue('speed', 1.2);
  }

  if (nodes.screenshot) {
    nodes.screenshot.setInputValue('format', 'png');
    nodes.screenshot.setInputValue('quality', 0.9);
  }

  console.log('✅ 复合脚本节点创建完成');
  console.log('   - 输入检测: 空格键');
  console.log('   - 条件判断: 血量 > 50');
  console.log('   - 动画播放: 跳跃动画');
  console.log('   - 渲染操作: 截图保存');

  return nodes;
}

/**
 * 显示节点统计信息
 */
function showNodeStatistics() {
  console.log('\n📊 节点统计信息');
  
  const allNodes = nodeRegistry.getAllNodeTypes();
  const categories = new Map<string, number>();
  
  // 统计各类别节点数量
  allNodes.forEach(nodeInfo => {
    const category = nodeInfo.category;
    categories.set(category, (categories.get(category) || 0) + 1);
  });
  
  console.log(`总节点数: ${allNodes.length}`);
  console.log('按类别分布:');
  
  for (const [category, count] of categories.entries()) {
    console.log(`  - ${category}: ${count}个`);
  }
}

// 运行示例
async function runExamples() {
  try {
    // 运行各个示例
    createAnimationControlExample();
    createInputHandlingExample();
    createLogicExample();
    createRenderingExample();
    createComplexExample();
    
    // 显示统计信息
    showNodeStatistics();
    
    console.log('\n🎉 所有示例运行完成！');
    console.log('新节点已成功集成到视觉脚本系统中，可以在编辑器中使用。');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error);
  }
}

// 启动示例
runExamples();
