/**
 * 逻辑节点预设
 * 提供各种逻辑运算和比较节点
 */

import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 数值比较节点
 */
export class ComparisonNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '数值比较';
    this.metadata.description = '比较两个数值';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'valueA',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '值A',
      required: true
    });

    this.addInput({
      name: 'valueB',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '值B',
      required: true
    });

    this.addInput({
      name: 'operator',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '比较操作符 (==, !=, <, <=, >, >=)',
      defaultValue: '=='
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '比较结果'
    });

    this.addOutput({
      name: 'difference',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '差值'
    });
  }

  public execute(): any {
    try {
      const valueA = this.getInputValue('valueA');
      const valueB = this.getInputValue('valueB');
      const operator = this.getInputValue('operator') || '==';

      if (valueA === undefined || valueB === undefined) {
        console.error('ComparisonNode: 缺少比较值');
        return null;
      }

      const numA = Number(valueA);
      const numB = Number(valueB);
      const difference = numA - numB;

      let result: boolean;
      switch (operator) {
        case '==':
        case '===':
          result = numA === numB;
          break;
        case '!=':
        case '!==':
          result = numA !== numB;
          break;
        case '<':
          result = numA < numB;
          break;
        case '<=':
          result = numA <= numB;
          break;
        case '>':
          result = numA > numB;
          break;
        case '>=':
          result = numA >= numB;
          break;
        default:
          console.error(`ComparisonNode: 不支持的操作符 ${operator}`);
          result = false;
      }

      this.setOutputValue('result', result);
      this.setOutputValue('difference', difference);

      return {
        result,
        difference
      };
    } catch (error) {
      console.error('ComparisonNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 字符串比较节点
 */
export class StringComparisonNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '字符串比较';
    this.metadata.description = '比较两个字符串';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'stringA',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字符串A',
      required: true
    });

    this.addInput({
      name: 'stringB',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字符串B',
      required: true
    });

    this.addInput({
      name: 'compareType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '比较类型 (equals, contains, startsWith, endsWith)',
      defaultValue: 'equals'
    });

    this.addInput({
      name: 'caseSensitive',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '大小写敏感',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '比较结果'
    });

    this.addOutput({
      name: 'similarity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '相似度 (0-1)'
    });
  }

  public execute(): any {
    try {
      let stringA = this.getInputValue('stringA') || '';
      let stringB = this.getInputValue('stringB') || '';
      const compareType = this.getInputValue('compareType') || 'equals';
      const caseSensitive = this.getInputValue('caseSensitive');

      if (!caseSensitive) {
        stringA = stringA.toLowerCase();
        stringB = stringB.toLowerCase();
      }

      let result: boolean;
      switch (compareType) {
        case 'equals':
          result = stringA === stringB;
          break;
        case 'contains':
          result = stringA.includes(stringB);
          break;
        case 'startsWith':
          result = stringA.startsWith(stringB);
          break;
        case 'endsWith':
          result = stringA.endsWith(stringB);
          break;
        default:
          console.error(`StringComparisonNode: 不支持的比较类型 ${compareType}`);
          result = false;
      }

      // 计算简单的相似度（基于编辑距离）
      const similarity = this.calculateSimilarity(stringA, stringB);

      this.setOutputValue('result', result);
      this.setOutputValue('similarity', similarity);

      return {
        result,
        similarity
      };
    } catch (error) {
      console.error('StringComparisonNode: 执行错误', error);
      return null;
    }
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;

    const distance = this.levenshteinDistance(str1, str2);
    return (maxLength - distance) / maxLength;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }
}

/**
 * 逻辑运算节点
 */
export class LogicalOperationNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '逻辑运算';
    this.metadata.description = '执行逻辑运算';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'valueA',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '值A',
      required: true
    });

    this.addInput({
      name: 'valueB',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '值B'
    });

    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '运算类型 (AND, OR, NOT, XOR)',
      defaultValue: 'AND'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '运算结果'
    });
  }

  public execute(): any {
    try {
      const valueA = Boolean(this.getInputValue('valueA'));
      const valueB = Boolean(this.getInputValue('valueB'));
      const operation = this.getInputValue('operation') || 'AND';

      let result: boolean;
      switch (operation.toUpperCase()) {
        case 'AND':
          result = valueA && valueB;
          break;
        case 'OR':
          result = valueA || valueB;
          break;
        case 'NOT':
          result = !valueA;
          break;
        case 'XOR':
          result = valueA !== valueB;
          break;
        default:
          console.error(`LogicalOperationNode: 不支持的运算类型 ${operation}`);
          result = false;
      }

      this.setOutputValue('result', result);

      return {
        result
      };
    } catch (error) {
      console.error('LogicalOperationNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 注册逻辑节点到节点注册表
 */
export function registerLogicNodes(registry: any): void {
  // 比较节点
  registry.registerNodeType({
    type: 'logic/comparison',
    category: 'Logic',
    description: '比较两个数值',
    constructor: ComparisonNode,
    icon: '⚖️',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'logic/stringComparison',
    category: 'Logic',
    description: '比较两个字符串',
    constructor: StringComparisonNode,
    icon: '📝',
    color: '#2196F3'
  });

  // 逻辑运算节点
  registry.registerNodeType({
    type: 'logic/logicalOperation',
    category: 'Logic',
    description: '执行逻辑运算',
    constructor: LogicalOperationNode,
    icon: '🔧',
    color: '#FF9800'
  });
}
