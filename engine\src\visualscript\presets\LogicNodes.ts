/**
 * 逻辑节点预设
 * 提供各种逻辑运算和比较节点
 */

import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 数值比较节点
 */
export class ComparisonNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '数值比较';
    this.metadata.description = '比较两个数值';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'valueA',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '值A',
      required: true
    });

    this.addInput({
      name: 'valueB',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '值B',
      required: true
    });

    this.addInput({
      name: 'operator',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '比较操作符 (==, !=, <, <=, >, >=)',
      defaultValue: '=='
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '比较结果'
    });

    this.addOutput({
      name: 'difference',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '差值'
    });
  }

  public execute(): any {
    try {
      const valueA = this.getInputValue('valueA');
      const valueB = this.getInputValue('valueB');
      const operator = this.getInputValue('operator') || '==';

      if (valueA === undefined || valueB === undefined) {
        console.error('ComparisonNode: 缺少比较值');
        return null;
      }

      const numA = Number(valueA);
      const numB = Number(valueB);
      const difference = numA - numB;

      let result: boolean;
      switch (operator) {
        case '==':
        case '===':
          result = numA === numB;
          break;
        case '!=':
        case '!==':
          result = numA !== numB;
          break;
        case '<':
          result = numA < numB;
          break;
        case '<=':
          result = numA <= numB;
          break;
        case '>':
          result = numA > numB;
          break;
        case '>=':
          result = numA >= numB;
          break;
        default:
          console.error(`ComparisonNode: 不支持的操作符 ${operator}`);
          result = false;
      }

      this.setOutputValue('result', result);
      this.setOutputValue('difference', difference);

      return {
        result,
        difference
      };
    } catch (error) {
      console.error('ComparisonNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 字符串比较节点
 */
export class StringComparisonNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '字符串比较';
    this.metadata.description = '比较两个字符串';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'stringA',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字符串A',
      required: true
    });

    this.addInput({
      name: 'stringB',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '字符串B',
      required: true
    });

    this.addInput({
      name: 'compareType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '比较类型 (equals, contains, startsWith, endsWith)',
      defaultValue: 'equals'
    });

    this.addInput({
      name: 'caseSensitive',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '大小写敏感',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '比较结果'
    });

    this.addOutput({
      name: 'similarity',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '相似度 (0-1)'
    });
  }

  public execute(): any {
    try {
      let stringA = this.getInputValue('stringA') || '';
      let stringB = this.getInputValue('stringB') || '';
      const compareType = this.getInputValue('compareType') || 'equals';
      const caseSensitive = this.getInputValue('caseSensitive');

      if (!caseSensitive) {
        stringA = stringA.toLowerCase();
        stringB = stringB.toLowerCase();
      }

      let result: boolean;
      switch (compareType) {
        case 'equals':
          result = stringA === stringB;
          break;
        case 'contains':
          result = stringA.includes(stringB);
          break;
        case 'startsWith':
          result = stringA.startsWith(stringB);
          break;
        case 'endsWith':
          result = stringA.endsWith(stringB);
          break;
        default:
          console.error(`StringComparisonNode: 不支持的比较类型 ${compareType}`);
          result = false;
      }

      // 计算简单的相似度（基于编辑距离）
      const similarity = this.calculateSimilarity(stringA, stringB);

      this.setOutputValue('result', result);
      this.setOutputValue('similarity', similarity);

      return {
        result,
        similarity
      };
    } catch (error) {
      console.error('StringComparisonNode: 执行错误', error);
      return null;
    }
  }

  private calculateSimilarity(str1: string, str2: string): number {
    const maxLength = Math.max(str1.length, str2.length);
    if (maxLength === 0) return 1;

    const distance = this.levenshteinDistance(str1, str2);
    return (maxLength - distance) / maxLength;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  }
}

/**
 * 逻辑运算节点
 */
export class LogicalOperationNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '逻辑运算';
    this.metadata.description = '执行逻辑运算';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'valueA',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '值A',
      required: true
    });

    this.addInput({
      name: 'valueB',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '值B'
    });

    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '运算类型 (AND, OR, NOT, XOR)',
      defaultValue: 'AND'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '运算结果'
    });
  }

  public execute(): any {
    try {
      const valueA = Boolean(this.getInputValue('valueA'));
      const valueB = Boolean(this.getInputValue('valueB'));
      const operation = this.getInputValue('operation') || 'AND';

      let result: boolean;
      switch (operation.toUpperCase()) {
        case 'AND':
          result = valueA && valueB;
          break;
        case 'OR':
          result = valueA || valueB;
          break;
        case 'NOT':
          result = !valueA;
          break;
        case 'XOR':
          result = valueA !== valueB;
          break;
        default:
          console.error(`LogicalOperationNode: 不支持的运算类型 ${operation}`);
          result = false;
      }

      this.setOutputValue('result', result);

      return {
        result
      };
    } catch (error) {
      console.error('LogicalOperationNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 对象比较节点
 */
export class ObjectComparisonNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '对象比较';
    this.metadata.description = '比较两个对象';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'objectA',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '对象A',
      required: true
    });

    this.addInput({
      name: 'objectB',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '对象B',
      required: true
    });

    this.addInput({
      name: 'compareDepth',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '比较深度 (-1为深度比较)',
      defaultValue: 1
    });

    // 输出插槽
    this.addOutput({
      name: 'isEqual',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否相等'
    });

    this.addOutput({
      name: 'differences',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '差异列表'
    });
  }

  public execute(): any {
    try {
      const objectA = this.getInputValue('objectA');
      const objectB = this.getInputValue('objectB');
      const compareDepth = this.getInputValue('compareDepth') || 1;

      if (objectA === undefined || objectB === undefined) {
        console.error('ObjectComparisonNode: 缺少比较对象');
        return null;
      }

      const result = this.deepCompare(objectA, objectB, compareDepth);

      this.setOutputValue('isEqual', result.isEqual);
      this.setOutputValue('differences', result.differences);

      return {
        isEqual: result.isEqual,
        differences: result.differences
      };
    } catch (error) {
      console.error('ObjectComparisonNode: 执行错误', error);
      return null;
    }
  }

  private deepCompare(obj1: any, obj2: any, depth: number, path: string = ''): any {
    const differences: string[] = [];

    if (depth === 0) {
      return { isEqual: obj1 === obj2, differences };
    }

    if (obj1 === obj2) {
      return { isEqual: true, differences };
    }

    if (obj1 == null || obj2 == null) {
      differences.push(`${path}: ${obj1} !== ${obj2}`);
      return { isEqual: false, differences };
    }

    if (typeof obj1 !== typeof obj2) {
      differences.push(`${path}: type mismatch (${typeof obj1} !== ${typeof obj2})`);
      return { isEqual: false, differences };
    }

    if (typeof obj1 === 'object') {
      const keys1 = Object.keys(obj1);
      const keys2 = Object.keys(obj2);
      const allKeys = new Set([...keys1, ...keys2]);

      let isEqual = true;
      for (const key of allKeys) {
        const newPath = path ? `${path}.${key}` : key;

        if (!(key in obj1)) {
          differences.push(`${newPath}: missing in first object`);
          isEqual = false;
        } else if (!(key in obj2)) {
          differences.push(`${newPath}: missing in second object`);
          isEqual = false;
        } else {
          const subResult = this.deepCompare(obj1[key], obj2[key], depth - 1, newPath);
          if (!subResult.isEqual) {
            isEqual = false;
            differences.push(...subResult.differences);
          }
        }
      }

      return { isEqual, differences };
    }

    differences.push(`${path}: ${obj1} !== ${obj2}`);
    return { isEqual: false, differences };
  }
}

/**
 * 数组比较节点
 */
export class ArrayComparisonNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '数组比较';
    this.metadata.description = '比较两个数组';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'arrayA',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组A',
      required: true
    });

    this.addInput({
      name: 'arrayB',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '数组B',
      required: true
    });

    this.addInput({
      name: 'compareMode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '比较模式 (strict, loose, length)',
      defaultValue: 'strict'
    });

    // 输出插槽
    this.addOutput({
      name: 'isEqual',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否相等'
    });

    this.addOutput({
      name: 'differenceIndices',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '差异索引'
    });
  }

  public execute(): any {
    try {
      const arrayA = this.getInputValue('arrayA');
      const arrayB = this.getInputValue('arrayB');
      const compareMode = this.getInputValue('compareMode') || 'strict';

      if (!Array.isArray(arrayA) || !Array.isArray(arrayB)) {
        console.error('ArrayComparisonNode: 输入不是数组');
        return null;
      }

      let isEqual = false;
      const differenceIndices: number[] = [];

      switch (compareMode) {
        case 'length':
          isEqual = arrayA.length === arrayB.length;
          break;
        case 'loose':
          isEqual = this.looseArrayCompare(arrayA, arrayB, differenceIndices);
          break;
        case 'strict':
        default:
          isEqual = this.strictArrayCompare(arrayA, arrayB, differenceIndices);
          break;
      }

      this.setOutputValue('isEqual', isEqual);
      this.setOutputValue('differenceIndices', differenceIndices);

      return {
        isEqual,
        differenceIndices
      };
    } catch (error) {
      console.error('ArrayComparisonNode: 执行错误', error);
      return null;
    }
  }

  private strictArrayCompare(arr1: any[], arr2: any[], differences: number[]): boolean {
    if (arr1.length !== arr2.length) {
      for (let i = 0; i < Math.max(arr1.length, arr2.length); i++) {
        differences.push(i);
      }
      return false;
    }

    let isEqual = true;
    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] !== arr2[i]) {
        differences.push(i);
        isEqual = false;
      }
    }

    return isEqual;
  }

  private looseArrayCompare(arr1: any[], arr2: any[], differences: number[]): boolean {
    if (arr1.length !== arr2.length) {
      for (let i = 0; i < Math.max(arr1.length, arr2.length); i++) {
        differences.push(i);
      }
      return false;
    }

    let isEqual = true;
    for (let i = 0; i < arr1.length; i++) {
      if (arr1[i] != arr2[i]) { // 使用 == 进行宽松比较
        differences.push(i);
        isEqual = false;
      }
    }

    return isEqual;
  }
}

/**
 * 切换节点
 */
export class ToggleNode extends FlowNode {
  private currentState: boolean = false;

  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['toggled', 'stateChanged']
    });

    this.metadata.name = '切换';
    this.metadata.description = '切换状态节点';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'initialState',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '初始状态',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'currentState',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '当前状态'
    });

    this.addOutput({
      name: 'stateChanged',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '状态是否改变'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const initialState = inputs.initialState;

      // 如果是第一次执行，设置初始状态
      if (this.currentState === undefined) {
        this.currentState = initialState || false;
      }

      const previousState = this.currentState;
      this.currentState = !this.currentState;
      const stateChanged = previousState !== this.currentState;

      this.setOutputValue('currentState', this.currentState);
      this.setOutputValue('stateChanged', stateChanged);

      return stateChanged ? 'stateChanged' : 'toggled';
    } catch (error) {
      console.error('ToggleNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 条件节点
 */
export class ConditionalNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['true', 'false']
    });

    this.metadata.name = '条件';
    this.metadata.description = '条件分支节点';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'condition',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '条件',
      required: true
    });

    this.addInput({
      name: 'trueValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '真值'
    });

    this.addInput({
      name: 'falseValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '假值'
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '选择的结果'
    });

    this.addOutput({
      name: 'conditionResult',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '条件结果'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const condition = Boolean(inputs.condition);
      const trueValue = inputs.trueValue;
      const falseValue = inputs.falseValue;

      const result = condition ? trueValue : falseValue;

      this.setOutputValue('result', result);
      this.setOutputValue('conditionResult', condition);

      return condition ? 'true' : 'false';
    } catch (error) {
      console.error('ConditionalNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 开关节点
 */
export class SwitchNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['case0', 'case1', 'case2', 'case3', 'default']
    });

    this.metadata.name = '开关';
    this.metadata.description = '多分支开关节点';
    this.metadata.category = 'Logic';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'selector',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '选择值',
      required: true
    });

    this.addInput({
      name: 'cases',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '分支列表',
      defaultValue: []
    });

    this.addInput({
      name: 'defaultValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.INPUT,
      description: '默认值'
    });

    // 输出插槽
    this.addOutput({
      name: 'selectedValue',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '选择的值'
    });

    this.addOutput({
      name: 'matchedIndex',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '匹配的索引'
    });

    this.addOutput({
      name: 'matched',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否匹配'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const selector = inputs.selector;
      const cases = inputs.cases || [];
      const defaultValue = inputs.defaultValue;

      let selectedValue = defaultValue;
      let matchedIndex = -1;
      let matched = false;
      let outputFlow = 'default';

      // 查找匹配的分支
      for (let i = 0; i < cases.length; i++) {
        if (cases[i] === selector || (cases[i] && cases[i].value === selector)) {
          selectedValue = cases[i].result || cases[i];
          matchedIndex = i;
          matched = true;
          outputFlow = `case${Math.min(i, 3)}`; // 最多支持4个命名输出
          break;
        }
      }

      this.setOutputValue('selectedValue', selectedValue);
      this.setOutputValue('matchedIndex', matchedIndex);
      this.setOutputValue('matched', matched);

      return outputFlow;
    } catch (error) {
      console.error('SwitchNode: 执行错误', error);
      return 'default';
    }
  }
}

/**
 * 注册逻辑节点到节点注册表
 */
export function registerLogicNodes(registry: any): void {
  // 比较节点
  registry.registerNodeType({
    type: 'logic/comparison',
    category: 'Logic',
    description: '比较两个数值',
    constructor: ComparisonNode,
    icon: '⚖️',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'logic/stringComparison',
    category: 'Logic',
    description: '比较两个字符串',
    constructor: StringComparisonNode,
    icon: '📝',
    color: '#2196F3'
  });

  // 逻辑运算节点
  registry.registerNodeType({
    type: 'logic/logicalOperation',
    category: 'Logic',
    description: '执行逻辑运算',
    constructor: LogicalOperationNode,
    icon: '🔧',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'logic/objectComparison',
    category: 'Logic',
    description: '比较两个对象',
    constructor: ObjectComparisonNode,
    icon: '🔍',
    color: '#9C27B0'
  });

  registry.registerNodeType({
    type: 'logic/arrayComparison',
    category: 'Logic',
    description: '比较两个数组',
    constructor: ArrayComparisonNode,
    icon: '📊',
    color: '#3F51B5'
  });

  registry.registerNodeType({
    type: 'logic/toggle',
    category: 'Logic',
    description: '切换状态节点',
    constructor: ToggleNode,
    icon: '🔄',
    color: '#E91E63'
  });

  registry.registerNodeType({
    type: 'logic/conditional',
    category: 'Logic',
    description: '条件分支节点',
    constructor: ConditionalNode,
    icon: '❓',
    color: '#607D8B'
  });

  registry.registerNodeType({
    type: 'logic/switch',
    category: 'Logic',
    description: '多分支开关节点',
    constructor: SwitchNode,
    icon: '🔀',
    color: '#795548'
  });
}
