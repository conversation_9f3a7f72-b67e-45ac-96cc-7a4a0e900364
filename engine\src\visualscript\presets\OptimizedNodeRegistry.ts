/**
 * 优化的节点注册系统
 * 统一管理所有视觉脚本节点的注册和配置
 */

import { NodeRegistry } from '../nodes/NodeRegistry';

// 导入所有节点注册函数
import { registerCoreNodes } from './CoreNodes';
import { registerUINodes } from './UINodes';
import { registerFileSystemNodes } from './FileSystemNodes';
import { registerImageProcessingNodes } from './ImageProcessingNodes';
import { registerMathNodes } from './MathNodes';
import { registerHTTPNodes } from './HTTPNodes';
import { registerJSONNodes } from './JSONNodes';
import { registerDateTimeNodes } from './DateTimeNodes';
// 第二阶段：中优先级节点
import { registerDatabaseNodes } from './DatabaseNodes';
import { registerNetworkNodes } from './NetworkNodes';
import { registerCryptographyNodes } from './CryptographyNodes';
import { registerAudioNodes } from './AudioNodes';
import { registerPhysicsNodes } from './PhysicsNodes';
// 新增节点
import { registerAnimationNodes } from './AnimationNodes';
import { registerInputNodes } from './InputNodes';
import { registerLogicNodes } from './LogicNodes';
import { registerRenderingNodes } from './RenderingNodes';
import { registerSceneNodes } from './SceneNodes';
import { registerResourceNodes } from './ResourceNodes';
import { registerAINodes } from './AINodes';

/**
 * 节点注册配置
 */
export interface NodeRegistrationConfig {
  /** 是否启用核心节点 */
  enableCoreNodes?: boolean;
  /** 是否启用UI节点 */
  enableUINodes?: boolean;
  /** 是否启用文件系统节点 */
  enableFileSystemNodes?: boolean;
  /** 是否启用图像处理节点 */
  enableImageProcessingNodes?: boolean;
  /** 是否启用数学节点 */
  enableMathNodes?: boolean;
  /** 是否启用HTTP节点 */
  enableHTTPNodes?: boolean;
  /** 是否启用JSON节点 */
  enableJSONNodes?: boolean;
  /** 是否启用时间日期节点 */
  enableDateTimeNodes?: boolean;
  /** 是否启用数据库节点 */
  enableDatabaseNodes?: boolean;
  /** 是否启用网络通信节点 */
  enableNetworkNodes?: boolean;
  /** 是否启用加密解密节点 */
  enableCryptographyNodes?: boolean;
  /** 是否启用音频处理节点 */
  enableAudioNodes?: boolean;
  /** 是否启用物理系统节点 */
  enablePhysicsNodes?: boolean;
  /** 是否启用动画节点 */
  enableAnimationNodes?: boolean;
  /** 是否启用输入节点 */
  enableInputNodes?: boolean;
  /** 是否启用逻辑节点 */
  enableLogicNodes?: boolean;
  /** 是否启用渲染节点 */
  enableRenderingNodes?: boolean;
  /** 是否启用场景管理节点 */
  enableSceneNodes?: boolean;
  /** 是否启用资源管理节点 */
  enableResourceNodes?: boolean;
  /** 是否启用AI节点 */
  enableAINodes?: boolean;
  /** 是否启用调试模式 */
  debugMode?: boolean;
  /** 自定义节点注册函数 */
  customRegistrations?: Array<(registry: NodeRegistry) => void>;
}

/**
 * 节点注册统计信息
 */
export interface NodeRegistrationStats {
  /** 总注册节点数 */
  totalNodes: number;
  /** 成功注册的节点数 */
  successfulRegistrations: number;
  /** 失败的注册数 */
  failedRegistrations: number;
  /** 按类别分组的节点数 */
  nodesByCategory: Record<string, number>;
  /** 注册错误列表 */
  errors: Array<{
    nodeType: string;
    error: string;
    category: string;
  }>;
  /** 注册耗时（毫秒） */
  registrationTime: number;
}

/**
 * 默认节点注册配置
 */
const DEFAULT_CONFIG: Required<NodeRegistrationConfig> = {
  enableCoreNodes: true,
  enableUINodes: true,
  enableFileSystemNodes: true,
  enableImageProcessingNodes: true,
  enableMathNodes: true,
  enableHTTPNodes: true,
  enableJSONNodes: true,
  enableDateTimeNodes: true,
  enableDatabaseNodes: true,
  enableNetworkNodes: true,
  enableCryptographyNodes: true,
  enableAudioNodes: true,
  enablePhysicsNodes: true,
  enableAnimationNodes: true,
  enableInputNodes: true,
  enableLogicNodes: true,
  enableRenderingNodes: true,
  enableSceneNodes: true,
  enableResourceNodes: true,
  enableAINodes: true,
  debugMode: false,
  customRegistrations: []
};

/**
 * 注册所有节点到指定的注册表
 * @param registry 节点注册表
 * @param config 注册配置
 * @returns 注册统计信息
 */
export function registerAllNodes(
  registry: NodeRegistry,
  config: NodeRegistrationConfig = {}
): NodeRegistrationStats {
  const startTime = performance.now();
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  const stats: NodeRegistrationStats = {
    totalNodes: 0,
    successfulRegistrations: 0,
    failedRegistrations: 0,
    nodesByCategory: {},
    errors: [],
    registrationTime: 0
  };

  // 节点注册函数映射
  const registrationFunctions = [
    {
      name: 'CoreNodes',
      category: '核心节点',
      enabled: finalConfig.enableCoreNodes,
      register: registerCoreNodes
    },
    {
      name: 'UINodes',
      category: 'UI节点',
      enabled: finalConfig.enableUINodes,
      register: registerUINodes
    },
    {
      name: 'FileSystemNodes',
      category: '文件系统',
      enabled: finalConfig.enableFileSystemNodes,
      register: registerFileSystemNodes
    },
    {
      name: 'ImageProcessingNodes',
      category: '图像处理',
      enabled: finalConfig.enableImageProcessingNodes,
      register: registerImageProcessingNodes
    },
    {
      name: 'MathNodes',
      category: '数学运算',
      enabled: finalConfig.enableMathNodes,
      register: registerMathNodes
    },
    {
      name: 'HTTPNodes',
      category: 'HTTP节点',
      enabled: finalConfig.enableHTTPNodes,
      register: registerHTTPNodes
    },
    {
      name: 'JSONNodes',
      category: 'JSON节点',
      enabled: finalConfig.enableJSONNodes,
      register: registerJSONNodes
    },
    {
      name: 'DateTimeNodes',
      category: '时间日期节点',
      enabled: finalConfig.enableDateTimeNodes,
      register: registerDateTimeNodes
    },
    // 第二阶段：中优先级节点
    {
      name: 'DatabaseNodes',
      category: '数据库节点',
      enabled: finalConfig.enableDatabaseNodes,
      register: registerDatabaseNodes
    },
    {
      name: 'NetworkNodes',
      category: '网络节点',
      enabled: finalConfig.enableNetworkNodes,
      register: registerNetworkNodes
    },
    {
      name: 'CryptographyNodes',
      category: '加密节点',
      enabled: finalConfig.enableCryptographyNodes,
      register: registerCryptographyNodes
    },
    {
      name: 'AudioNodes',
      category: '音频节点',
      enabled: finalConfig.enableAudioNodes,
      register: registerAudioNodes
    },
    {
      name: 'PhysicsNodes',
      category: '物理节点',
      enabled: finalConfig.enablePhysicsNodes,
      register: registerPhysicsNodes
    },
    // 新增节点
    {
      name: 'AnimationNodes',
      category: '动画节点',
      enabled: finalConfig.enableAnimationNodes,
      register: registerAnimationNodes
    },
    {
      name: 'InputNodes',
      category: '输入节点',
      enabled: finalConfig.enableInputNodes,
      register: registerInputNodes
    },
    {
      name: 'LogicNodes',
      category: '逻辑节点',
      enabled: finalConfig.enableLogicNodes,
      register: registerLogicNodes
    },
    {
      name: 'RenderingNodes',
      category: '渲染节点',
      enabled: finalConfig.enableRenderingNodes,
      register: registerRenderingNodes
    },
    {
      name: 'SceneNodes',
      category: '场景管理节点',
      enabled: finalConfig.enableSceneNodes,
      register: registerSceneNodes
    },
    {
      name: 'ResourceNodes',
      category: '资源管理节点',
      enabled: finalConfig.enableResourceNodes,
      register: registerResourceNodes
    },
    {
      name: 'AINodes',
      category: 'AI节点',
      enabled: finalConfig.enableAINodes,
      register: registerAINodes
    }
  ];

  // 执行节点注册
  for (const regFunc of registrationFunctions) {
    if (!regFunc.enabled) {
      if (finalConfig.debugMode) {
        console.log(`跳过注册 ${regFunc.name} (已禁用)`);
      }
      continue;
    }

    try {
      const beforeCount = registry.getAllNodeTypes().length;

      // 执行注册
      regFunc.register(registry);

      const afterCount = registry.getAllNodeTypes().length;
      const registeredCount = afterCount - beforeCount;
      
      stats.successfulRegistrations += registeredCount;
      stats.nodesByCategory[regFunc.category] = registeredCount;
      
      if (finalConfig.debugMode) {
        console.log(`✅ 成功注册 ${regFunc.name}: ${registeredCount} 个节点`);
      }
      
    } catch (error) {
      stats.failedRegistrations++;
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      stats.errors.push({
        nodeType: regFunc.name,
        error: errorMessage,
        category: regFunc.category
      });
      
      if (finalConfig.debugMode) {
        console.error(`❌ 注册 ${regFunc.name} 失败:`, errorMessage);
      }
    }
  }

  // 执行自定义注册函数
  for (const customReg of finalConfig.customRegistrations) {
    try {
      const beforeCount = registry.getAllNodeTypes().length;
      customReg(registry);
      const afterCount = registry.getAllNodeTypes().length;
      const registeredCount = afterCount - beforeCount;
      
      stats.successfulRegistrations += registeredCount;
      stats.nodesByCategory['自定义节点'] = 
        (stats.nodesByCategory['自定义节点'] || 0) + registeredCount;
        
      if (finalConfig.debugMode) {
        console.log(`✅ 成功注册自定义节点: ${registeredCount} 个节点`);
      }
      
    } catch (error) {
      stats.failedRegistrations++;
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      
      stats.errors.push({
        nodeType: '自定义节点',
        error: errorMessage,
        category: '自定义节点'
      });
      
      if (finalConfig.debugMode) {
        console.error(`❌ 注册自定义节点失败:`, errorMessage);
      }
    }
  }

  // 计算总节点数和注册时间
  stats.totalNodes = stats.successfulRegistrations + stats.failedRegistrations;
  stats.registrationTime = performance.now() - startTime;

  if (finalConfig.debugMode) {
    console.log(`\n📊 节点注册统计:`);
    console.log(`总节点数: ${stats.totalNodes}`);
    console.log(`成功注册: ${stats.successfulRegistrations}`);
    console.log(`注册失败: ${stats.failedRegistrations}`);
    console.log(`注册耗时: ${stats.registrationTime.toFixed(2)}ms`);
    console.log(`按类别分组:`, stats.nodesByCategory);
    
    if (stats.errors.length > 0) {
      console.log(`\n❌ 注册错误:`);
      stats.errors.forEach(error => {
        console.log(`  - ${error.nodeType}: ${error.error}`);
      });
    }
  }

  return stats;
}

/**
 * 获取节点注册统计信息
 * @param registry 节点注册表
 * @returns 统计信息
 */
export function getNodeRegistrationStats(registry: NodeRegistry): NodeRegistrationStats {
  const nodeTypes = registry.getAllNodeTypes();
  const stats: NodeRegistrationStats = {
    totalNodes: nodeTypes.length,
    successfulRegistrations: nodeTypes.length,
    failedRegistrations: 0,
    nodesByCategory: {},
    errors: [],
    registrationTime: 0
  };

  // 按类别统计节点
  for (const nodeInfo of nodeTypes) {
    if (nodeInfo) {
      const category = nodeInfo.category || '未分类';
      stats.nodesByCategory[category] = (stats.nodesByCategory[category] || 0) + 1;
    }
  }

  return stats;
}

/**
 * 验证节点注册的完整性
 * @param registry 节点注册表
 * @returns 验证结果
 */
export function validateNodeRegistration(registry: NodeRegistry): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  const nodeTypes = registry.getAllNodeTypes();

  // 检查是否有注册的节点
  if (nodeTypes.length === 0) {
    issues.push('没有注册任何节点');
    recommendations.push('请确保调用了 registerAllNodes 函数');
  }

  // 检查核心节点是否存在
  const coreNodeTypes = ['core/events/onStart', 'core/events/onUpdate', 'core/flow/sequence'];
  for (const coreType of coreNodeTypes) {
    if (!nodeTypes.some(info => info.type === coreType)) {
      issues.push(`缺少核心节点: ${coreType}`);
      recommendations.push('请确保启用了核心节点注册');
    }
  }

  // 检查节点信息完整性
  let incompleteNodes = 0;
  for (const nodeInfo of nodeTypes) {
    if (!nodeInfo) {
      issues.push(`节点信息缺失`);
      incompleteNodes++;
    } else {
      if (!nodeInfo.label) {
        issues.push(`节点类型 ${nodeInfo.type} 缺少名称`);
      }
      if (!nodeInfo.category) {
        issues.push(`节点类型 ${nodeInfo.type} 缺少类别`);
      }
      if (!nodeInfo.factory && !nodeInfo.constructor) {
        issues.push(`节点类型 ${nodeInfo.type} 缺少工厂函数或构造函数`);
      }
    }
  }

  if (incompleteNodes > 0) {
    recommendations.push(`有 ${incompleteNodes} 个节点信息不完整，请检查节点注册代码`);
  }

  // 检查类别分布
  const stats = getNodeRegistrationStats(registry);
  const categories = Object.keys(stats.nodesByCategory);
  
  if (categories.length < 3) {
    recommendations.push('建议注册更多类别的节点以提供完整功能');
  }

  return {
    isValid: issues.length === 0,
    issues,
    recommendations
  };
}

/**
 * 打印节点注册报告
 * @param registry 节点注册表
 */
export function printNodeRegistrationReport(registry: NodeRegistry): void {
  console.log('\n=== 视觉脚本节点注册报告 ===\n');
  
  const stats = getNodeRegistrationStats(registry);
  const validation = validateNodeRegistration(registry);
  
  console.log(`📊 统计信息:`);
  console.log(`  总节点数: ${stats.totalNodes}`);
  console.log(`  节点类别数: ${Object.keys(stats.nodesByCategory).length}`);
  
  console.log(`\n📋 按类别分组:`);
  for (const [category, count] of Object.entries(stats.nodesByCategory)) {
    console.log(`  ${category}: ${count} 个节点`);
  }
  
  console.log(`\n✅ 验证结果: ${validation.isValid ? '通过' : '失败'}`);
  
  if (validation.issues.length > 0) {
    console.log(`\n❌ 发现问题:`);
    validation.issues.forEach(issue => console.log(`  - ${issue}`));
  }
  
  if (validation.recommendations.length > 0) {
    console.log(`\n💡 建议:`);
    validation.recommendations.forEach(rec => console.log(`  - ${rec}`));
  }
  
  console.log('\n=== 报告结束 ===\n');
}
