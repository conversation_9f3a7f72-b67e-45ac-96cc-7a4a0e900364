/**
 * 动画节点预设
 * 提供各种动画控制和处理节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { EventNode } from '../nodes/EventNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 播放动画节点
 */
export class PlayAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '播放动画';
    this.metadata.description = '播放指定的动画';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '目标实体',
      required: true
    });

    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画名称',
      required: true
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: false
    });

    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '播放速度',
      defaultValue: 1.0
    });

    // 输出插槽
    this.addOutput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '动画实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const entity = inputs.entity;
      const animationName = inputs.animationName;
      const loop = inputs.loop || false;
      const speed = inputs.speed || 1.0;

      if (!entity || !animationName) {
        console.error('PlayAnimationNode: 缺少必要的输入参数');
        return 'failed';
      }

      // 获取动画组件
      const animationComponent = entity.getComponent('Animation');
      if (!animationComponent) {
        console.error('PlayAnimationNode: 实体没有动画组件');
        return 'failed';
      }

      // 播放动画
      const animationInstance = animationComponent.play(animationName, {
        loop: loop,
        speed: speed
      });

      if (animationInstance) {
        this.setOutputValue('animationInstance', animationInstance);
        return 'success';
      } else {
        console.error(`PlayAnimationNode: 无法播放动画 ${animationName}`);
        return 'failed';
      }
    } catch (error) {
      console.error('PlayAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 停止动画节点
 */
export class StopAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '停止动画';
    this.metadata.description = '停止指定的动画';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例或实体',
      required: true
    });

    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画名称（可选）'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const target = inputs.target;
      const animationName = inputs.animationName;

      if (!target) {
        console.error('StopAnimationNode: 缺少目标参数');
        return 'failed';
      }

      // 如果目标是动画实例
      if (target.stop && typeof target.stop === 'function') {
        target.stop();
        return 'success';
      }

      // 如果目标是实体
      const animationComponent = target.getComponent && target.getComponent('Animation');
      if (animationComponent) {
        if (animationName) {
          animationComponent.stop(animationName);
        } else {
          animationComponent.stopAll();
        }
        return 'success';
      }

      console.error('StopAnimationNode: 无效的目标类型');
      return 'failed';
    } catch (error) {
      console.error('StopAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 暂停动画节点
 */
export class PauseAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '暂停动画';
    this.metadata.description = '暂停或恢复动画播放';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    this.addInput({
      name: 'paused',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '暂停状态',
      defaultValue: true
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animationInstance = inputs.animationInstance;
      const paused = inputs.paused;

      if (!animationInstance) {
        console.error('PauseAnimationNode: 缺少动画实例');
        return 'failed';
      }

      if (paused) {
        if (animationInstance.pause && typeof animationInstance.pause === 'function') {
          animationInstance.pause();
        } else {
          console.error('PauseAnimationNode: 动画实例不支持暂停');
          return 'failed';
        }
      } else {
        if (animationInstance.resume && typeof animationInstance.resume === 'function') {
          animationInstance.resume();
        } else if (animationInstance.play && typeof animationInstance.play === 'function') {
          animationInstance.play();
        } else {
          console.error('PauseAnimationNode: 动画实例不支持恢复');
          return 'failed';
        }
      }

      return 'success';
    } catch (error) {
      console.error('PauseAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 设置动画速度节点
 */
export class SetAnimationSpeedNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置动画速度';
    this.metadata.description = '设置动画播放速度';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '速度倍率',
      defaultValue: 1.0
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animationInstance = inputs.animationInstance;
      const speed = inputs.speed || 1.0;

      if (!animationInstance) {
        console.error('SetAnimationSpeedNode: 缺少动画实例');
        return 'failed';
      }

      if (animationInstance.setSpeed && typeof animationInstance.setSpeed === 'function') {
        animationInstance.setSpeed(speed);
        return 'success';
      } else if (animationInstance.speed !== undefined) {
        animationInstance.speed = speed;
        return 'success';
      } else {
        console.error('SetAnimationSpeedNode: 动画实例不支持设置速度');
        return 'failed';
      }
    } catch (error) {
      console.error('SetAnimationSpeedNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 获取动画时间节点
 */
export class GetAnimationTimeNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '获取动画时间';
    this.metadata.description = '获取动画的时间信息';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    // 输出插槽
    this.addOutput({
      name: 'currentTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '当前时间'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '总时长'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '进度百分比'
    });
  }

  public execute(): any {
    try {
      const animationInstance = this.getInputValue('animationInstance');

      if (!animationInstance) {
        console.error('GetAnimationTimeNode: 缺少动画实例');
        return null;
      }

      const currentTime = animationInstance.currentTime || 0;
      const duration = animationInstance.duration || 0;
      const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

      this.setOutputValue('currentTime', currentTime);
      this.setOutputValue('duration', duration);
      this.setOutputValue('progress', progress);

      return {
        currentTime,
        duration,
        progress
      };
    } catch (error) {
      console.error('GetAnimationTimeNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 设置动画时间节点
 */
export class SetAnimationTimeNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置动画时间';
    this.metadata.description = '设置动画播放时间';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    this.addInput({
      name: 'time',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '时间',
      defaultValue: 0
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animationInstance = inputs.animationInstance;
      const time = inputs.time || 0;

      if (!animationInstance) {
        console.error('SetAnimationTimeNode: 缺少动画实例');
        return 'failed';
      }

      if (animationInstance.setTime && typeof animationInstance.setTime === 'function') {
        animationInstance.setTime(time);
        return 'success';
      } else if (animationInstance.currentTime !== undefined) {
        animationInstance.currentTime = time;
        return 'success';
      } else {
        console.error('SetAnimationTimeNode: 动画实例不支持设置时间');
        return 'failed';
      }
    } catch (error) {
      console.error('SetAnimationTimeNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 检查动画播放状态节点
 */
export class IsAnimationPlayingNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '检查动画状态';
    this.metadata.description = '检查动画播放状态';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    // 输出插槽
    this.addOutput({
      name: 'isPlaying',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否播放中'
    });

    this.addOutput({
      name: 'isPaused',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否暂停'
    });

    this.addOutput({
      name: 'isLooping',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否循环'
    });
  }

  public execute(): any {
    try {
      const animationInstance = this.getInputValue('animationInstance');

      if (!animationInstance) {
        console.error('IsAnimationPlayingNode: 缺少动画实例');
        return null;
      }

      const isPlaying = animationInstance.isPlaying || false;
      const isPaused = animationInstance.isPaused || false;
      const isLooping = animationInstance.isLooping || false;

      this.setOutputValue('isPlaying', isPlaying);
      this.setOutputValue('isPaused', isPaused);
      this.setOutputValue('isLooping', isLooping);

      return {
        isPlaying,
        isPaused,
        isLooping
      };
    } catch (error) {
      console.error('IsAnimationPlayingNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 动画混合节点
 */
export class AnimationBlendNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '动画混合';
    this.metadata.description = '混合两个动画';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animation1',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画1',
      required: true
    });

    this.addInput({
      name: 'animation2',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画2',
      required: true
    });

    this.addInput({
      name: 'blendWeight',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '混合权重 (0-1)',
      defaultValue: 0.5
    });

    this.addInput({
      name: 'blendMode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '混合模式',
      defaultValue: 'linear'
    });

    // 输出插槽
    this.addOutput({
      name: 'blendedAnimation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '混合结果'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animation1 = inputs.animation1;
      const animation2 = inputs.animation2;
      const blendWeight = Math.max(0, Math.min(1, inputs.blendWeight || 0.5));
      const blendMode = inputs.blendMode || 'linear';

      if (!animation1 || !animation2) {
        console.error('AnimationBlendNode: 缺少动画输入');
        return 'failed';
      }

      // 创建混合动画实例
      const blendedAnimation = {
        animation1,
        animation2,
        blendWeight,
        blendMode,
        isBlended: true,
        play: () => {
          animation1.play();
          animation2.play();
        },
        stop: () => {
          animation1.stop();
          animation2.stop();
        },
        pause: () => {
          animation1.pause();
          animation2.pause();
        },
        setWeight: (weight: number) => {
          blendedAnimation.blendWeight = Math.max(0, Math.min(1, weight));
        }
      };

      this.setOutputValue('blendedAnimation', blendedAnimation);
      return 'success';
    } catch (error) {
      console.error('AnimationBlendNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 交叉淡化动画节点
 */
export class CrossFadeAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed', 'completed']
    });

    this.metadata.name = '交叉淡化动画';
    this.metadata.description = '在两个动画之间进行交叉淡化';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'currentAnimation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '当前动画',
      required: true
    });

    this.addInput({
      name: 'targetAnimation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '目标动画',
      required: true
    });

    this.addInput({
      name: 'fadeTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '淡化时间（秒）',
      defaultValue: 1.0
    });

    // 输出插槽
    this.addOutput({
      name: 'fadeInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '淡化实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const currentAnimation = inputs.currentAnimation;
      const targetAnimation = inputs.targetAnimation;
      const fadeTime = inputs.fadeTime || 1.0;

      if (!currentAnimation || !targetAnimation) {
        console.error('CrossFadeAnimationNode: 缺少动画输入');
        return 'failed';
      }

      // 创建交叉淡化实例
      const fadeInstance = {
        currentAnimation,
        targetAnimation,
        fadeTime,
        startTime: Date.now(),
        isActive: true,
        currentWeight: 1.0,
        targetWeight: 0.0,

        update: () => {
          const elapsed = (Date.now() - fadeInstance.startTime) / 1000;
          const progress = Math.min(elapsed / fadeTime, 1.0);

          fadeInstance.currentWeight = 1.0 - progress;
          fadeInstance.targetWeight = progress;

          if (progress >= 1.0) {
            fadeInstance.isActive = false;
            return 'completed';
          }

          return 'success';
        },

        stop: () => {
          fadeInstance.isActive = false;
          currentAnimation.stop && currentAnimation.stop();
          targetAnimation.stop && targetAnimation.stop();
        }
      };

      // 开始播放目标动画
      if (targetAnimation.play && typeof targetAnimation.play === 'function') {
        targetAnimation.play();
      }

      this.setOutputValue('fadeInstance', fadeInstance);
      return 'success';
    } catch (error) {
      console.error('CrossFadeAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 分层动画节点
 */
export class LayeredAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '分层动画';
    this.metadata.description = '在基础动画上叠加分层动画';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'baseAnimation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '基础动画',
      required: true
    });

    this.addInput({
      name: 'layerAnimation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '分层动画',
      required: true
    });

    this.addInput({
      name: 'blendMode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '混合模式 (override, additive)',
      defaultValue: 'override'
    });

    this.addInput({
      name: 'weight',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '分层权重 (0-1)',
      defaultValue: 1.0
    });

    // 输出插槽
    this.addOutput({
      name: 'layeredResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '分层结果'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const baseAnimation = inputs.baseAnimation;
      const layerAnimation = inputs.layerAnimation;
      const blendMode = inputs.blendMode || 'override';
      const weight = Math.max(0, Math.min(1, inputs.weight || 1.0));

      if (!baseAnimation || !layerAnimation) {
        console.error('LayeredAnimationNode: 缺少动画输入');
        return 'failed';
      }

      // 创建分层动画实例
      const layeredResult = {
        baseAnimation,
        layerAnimation,
        blendMode,
        weight,
        isLayered: true,

        play: () => {
          baseAnimation.play && baseAnimation.play();
          layerAnimation.play && layerAnimation.play();
        },

        stop: () => {
          baseAnimation.stop && baseAnimation.stop();
          layerAnimation.stop && layerAnimation.stop();
        },

        pause: () => {
          baseAnimation.pause && baseAnimation.pause();
          layerAnimation.pause && layerAnimation.pause();
        },

        setWeight: (newWeight: number) => {
          layeredResult.weight = Math.max(0, Math.min(1, newWeight));
        }
      };

      this.setOutputValue('layeredResult', layeredResult);
      return 'success';
    } catch (error) {
      console.error('LayeredAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 叠加动画节点
 */
export class AdditiveAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '叠加动画';
    this.metadata.description = '将动画叠加到基础动画上';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'baseAnimation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '基础动画',
      required: true
    });

    this.addInput({
      name: 'additiveAnimation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '叠加动画',
      required: true
    });

    this.addInput({
      name: 'additiveWeight',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '叠加权重 (0-1)',
      defaultValue: 1.0
    });

    // 输出插槽
    this.addOutput({
      name: 'additiveResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '叠加结果'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const baseAnimation = inputs.baseAnimation;
      const additiveAnimation = inputs.additiveAnimation;
      const additiveWeight = Math.max(0, Math.min(1, inputs.additiveWeight || 1.0));

      if (!baseAnimation || !additiveAnimation) {
        console.error('AdditiveAnimationNode: 缺少动画输入');
        return 'failed';
      }

      // 创建叠加动画实例
      const additiveResult = {
        baseAnimation,
        additiveAnimation,
        additiveWeight,
        isAdditive: true,

        play: () => {
          baseAnimation.play && baseAnimation.play();
          additiveAnimation.play && additiveAnimation.play();
        },

        stop: () => {
          baseAnimation.stop && baseAnimation.stop();
          additiveAnimation.stop && additiveAnimation.stop();
        },

        pause: () => {
          baseAnimation.pause && baseAnimation.pause();
          additiveAnimation.pause && additiveAnimation.pause();
        },

        setAdditiveWeight: (weight: number) => {
          additiveResult.additiveWeight = Math.max(0, Math.min(1, weight));
        }
      };

      this.setOutputValue('additiveResult', additiveResult);
      return 'success';
    } catch (error) {
      console.error('AdditiveAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 遮罩动画节点
 */
export class MaskAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '遮罩动画';
    this.metadata.description = '使用遮罩控制动画影响范围';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    this.addInput({
      name: 'boneMask',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '骨骼遮罩',
      required: true
    });

    this.addInput({
      name: 'maskWeight',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '遮罩权重 (0-1)',
      defaultValue: 1.0
    });

    // 输出插槽
    this.addOutput({
      name: 'maskedResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '遮罩结果'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animation = inputs.animation;
      const boneMask = inputs.boneMask;
      const maskWeight = Math.max(0, Math.min(1, inputs.maskWeight || 1.0));

      if (!animation || !boneMask) {
        console.error('MaskAnimationNode: 缺少动画或遮罩输入');
        return 'failed';
      }

      // 创建遮罩动画实例
      const maskedResult = {
        animation,
        boneMask,
        maskWeight,
        isMasked: true,

        play: () => {
          animation.play && animation.play();
        },

        stop: () => {
          animation.stop && animation.stop();
        },

        pause: () => {
          animation.pause && animation.pause();
        },

        setMaskWeight: (weight: number) => {
          maskedResult.maskWeight = Math.max(0, Math.min(1, weight));
        },

        updateMask: (newMask: any) => {
          maskedResult.boneMask = newMask;
        }
      };

      this.setOutputValue('maskedResult', maskedResult);
      return 'success';
    } catch (error) {
      console.error('MaskAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 混合树节点
 */
export class BlendTreeNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '混合树';
    this.metadata.description = '多动画混合树';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationList',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '动画列表',
      required: true
    });

    this.addInput({
      name: 'blendParameters',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '混合参数',
      required: true
    });

    this.addInput({
      name: 'blendType',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '混合类型 (1D, 2D, directional)',
      defaultValue: '1D'
    });

    // 输出插槽
    this.addOutput({
      name: 'blendResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '混合结果'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animationList = inputs.animationList;
      const blendParameters = inputs.blendParameters;
      const blendType = inputs.blendType || '1D';

      if (!animationList || !Array.isArray(animationList) || animationList.length === 0) {
        console.error('BlendTreeNode: 缺少有效的动画列表');
        return 'failed';
      }

      if (!blendParameters) {
        console.error('BlendTreeNode: 缺少混合参数');
        return 'failed';
      }

      // 创建混合树实例
      const blendResult = {
        animationList,
        blendParameters,
        blendType,
        isBlendTree: true,
        currentWeights: new Array(animationList.length).fill(0),

        calculateWeights: () => {
          // 根据混合类型计算权重
          switch (blendType) {
            case '1D':
              return this.calculate1DBlend(blendParameters, animationList.length);
            case '2D':
              return this.calculate2DBlend(blendParameters, animationList);
            case 'directional':
              return this.calculateDirectionalBlend(blendParameters, animationList);
            default:
              return new Array(animationList.length).fill(1.0 / animationList.length);
          }
        },

        play: () => {
          blendResult.currentWeights = blendResult.calculateWeights();
          animationList.forEach((anim: any, index: number) => {
            if (blendResult.currentWeights[index] > 0 && anim.play) {
              anim.play();
            }
          });
        },

        stop: () => {
          animationList.forEach((anim: any) => {
            anim.stop && anim.stop();
          });
        },

        updateParameters: (newParams: any) => {
          blendResult.blendParameters = { ...blendResult.blendParameters, ...newParams };
          blendResult.currentWeights = blendResult.calculateWeights();
        }
      };

      this.setOutputValue('blendResult', blendResult);
      return 'success';
    } catch (error) {
      console.error('BlendTreeNode: 执行错误', error);
      return 'failed';
    }
  }

  private calculate1DBlend(parameters: any, animCount: number): number[] {
    const value = parameters.value || 0;
    const weights = new Array(animCount).fill(0);

    if (animCount <= 1) {
      weights[0] = 1.0;
      return weights;
    }

    const step = 1.0 / (animCount - 1);
    const index = Math.floor(value / step);
    const t = (value % step) / step;

    if (index < animCount - 1) {
      weights[index] = 1.0 - t;
      weights[index + 1] = t;
    } else {
      weights[animCount - 1] = 1.0;
    }

    return weights;
  }

  private calculate2DBlend(parameters: any, animList: any[]): number[] {
    const x = parameters.x || 0;
    const y = parameters.y || 0;
    const weights = new Array(animList.length).fill(0);

    // 简化的2D混合计算
    animList.forEach((anim: any, index: number) => {
      const animX = anim.blendPosition?.x || 0;
      const animY = anim.blendPosition?.y || 0;
      const distance = Math.sqrt((x - animX) ** 2 + (y - animY) ** 2);
      weights[index] = Math.max(0, 1.0 - distance);
    });

    // 归一化权重
    const totalWeight = weights.reduce((sum, w) => sum + w, 0);
    if (totalWeight > 0) {
      weights.forEach((w, i) => weights[i] = w / totalWeight);
    }

    return weights;
  }

  private calculateDirectionalBlend(parameters: any, animList: any[]): number[] {
    const direction = parameters.direction || 0;
    const weights = new Array(animList.length).fill(0);

    // 基于方向的混合计算
    animList.forEach((anim: any, index: number) => {
      const animDirection = anim.direction || 0;
      const angleDiff = Math.abs(direction - animDirection);
      const normalizedDiff = Math.min(angleDiff, 360 - angleDiff);
      weights[index] = Math.max(0, 1.0 - normalizedDiff / 180);
    });

    // 归一化权重
    const totalWeight = weights.reduce((sum, w) => sum + w, 0);
    if (totalWeight > 0) {
      weights.forEach((w, i) => weights[i] = w / totalWeight);
    }

    return weights;
  }
}

/**
 * IK求解器节点
 */
export class IKSolverNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = 'IK求解器';
    this.metadata.description = 'IK反向动力学求解器';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'targetPosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '目标位置',
      required: true
    });

    this.addInput({
      name: 'ikChain',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: 'IK链（骨骼列表）',
      required: true
    });

    this.addInput({
      name: 'constraints',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '约束条件'
    });

    this.addInput({
      name: 'iterations',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '迭代次数',
      defaultValue: 10
    });

    // 输出插槽
    this.addOutput({
      name: 'solveResult',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '求解结果'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const targetPosition = inputs.targetPosition;
      const ikChain = inputs.ikChain;
      const constraints = inputs.constraints || {};
      const iterations = Math.max(1, inputs.iterations || 10);

      if (!targetPosition || !ikChain || !Array.isArray(ikChain)) {
        console.error('IKSolverNode: 缺少目标位置或IK链');
        return 'failed';
      }

      // 创建IK求解结果
      const solveResult = {
        targetPosition,
        ikChain,
        constraints,
        iterations,
        isIKSolved: true,

        solve: () => {
          // 简化的IK求解算法（FABRIK或CCD）
          return this.solveFABRIK(ikChain, targetPosition, iterations, constraints);
        },

        apply: () => {
          const solution = solveResult.solve();
          if (solution.success) {
            // 应用求解结果到骨骼
            ikChain.forEach((bone: any, index: number) => {
              if (solution.positions[index] && bone.setPosition) {
                bone.setPosition(solution.positions[index]);
              }
              if (solution.rotations[index] && bone.setRotation) {
                bone.setRotation(solution.rotations[index]);
              }
            });
          }
          return solution.success;
        }
      };

      this.setOutputValue('solveResult', solveResult);
      return 'success';
    } catch (error) {
      console.error('IKSolverNode: 执行错误', error);
      return 'failed';
    }
  }

  private solveFABRIK(chain: any[], target: any, iterations: number, constraints: any): any {
    // 简化的FABRIK算法实现
    const positions = chain.map(bone => bone.position || { x: 0, y: 0, z: 0 });
    const lengths = [];

    // 计算骨骼长度
    for (let i = 0; i < chain.length - 1; i++) {
      const p1 = positions[i];
      const p2 = positions[i + 1];
      lengths[i] = Math.sqrt((p2.x - p1.x) ** 2 + (p2.y - p1.y) ** 2 + (p2.z - p1.z) ** 2);
    }

    const rootPosition = { ...positions[0] };

    for (let iter = 0; iter < iterations; iter++) {
      // Forward reaching
      positions[positions.length - 1] = { ...target };

      for (let i = positions.length - 2; i >= 0; i--) {
        const direction = this.normalize(this.subtract(positions[i], positions[i + 1]));
        positions[i] = this.add(positions[i + 1], this.multiply(direction, lengths[i]));
      }

      // Backward reaching
      positions[0] = { ...rootPosition };

      for (let i = 0; i < positions.length - 1; i++) {
        const direction = this.normalize(this.subtract(positions[i + 1], positions[i]));
        positions[i + 1] = this.add(positions[i], this.multiply(direction, lengths[i]));
      }

      // 检查是否达到目标
      const endEffector = positions[positions.length - 1];
      const distance = this.distance(endEffector, target);
      if (distance < 0.01) break; // 精度阈值
    }

    return {
      success: true,
      positions,
      rotations: this.calculateRotations(positions, chain)
    };
  }

  private normalize(v: any): any {
    const length = Math.sqrt(v.x * v.x + v.y * v.y + v.z * v.z);
    return length > 0 ? { x: v.x / length, y: v.y / length, z: v.z / length } : { x: 0, y: 0, z: 0 };
  }

  private subtract(a: any, b: any): any {
    return { x: a.x - b.x, y: a.y - b.y, z: a.z - b.z };
  }

  private add(a: any, b: any): any {
    return { x: a.x + b.x, y: a.y + b.y, z: a.z + b.z };
  }

  private multiply(v: any, scalar: number): any {
    return { x: v.x * scalar, y: v.y * scalar, z: v.z * scalar };
  }

  private distance(a: any, b: any): number {
    return Math.sqrt((a.x - b.x) ** 2 + (a.y - b.y) ** 2 + (a.z - b.z) ** 2);
  }

  private calculateRotations(positions: any[], chain: any[]): any[] {
    // 简化的旋转计算
    const rotations = [];
    for (let i = 0; i < positions.length - 1; i++) {
      const direction = this.normalize(this.subtract(positions[i + 1], positions[i]));
      // 这里应该计算实际的四元数旋转，简化为方向向量
      rotations[i] = direction;
    }
    return rotations;
  }
}

/**
 * 注册动画节点到节点注册表
 */
export function registerAnimationNodes(registry: any): void {
  // 基础动画节点
  registry.registerNodeType({
    type: 'animation/playAnimation',
    category: 'Animation',
    description: '播放指定的动画',
    constructor: PlayAnimationNode,
    icon: '▶️',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'animation/stopAnimation',
    category: 'Animation',
    description: '停止指定的动画',
    constructor: StopAnimationNode,
    icon: '⏹️',
    color: '#F44336'
  });

  registry.registerNodeType({
    type: 'animation/pauseAnimation',
    category: 'Animation',
    description: '暂停或恢复动画播放',
    constructor: PauseAnimationNode,
    icon: '⏸️',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'animation/setAnimationSpeed',
    category: 'Animation',
    description: '设置动画播放速度',
    constructor: SetAnimationSpeedNode,
    icon: '⚡',
    color: '#2196F3'
  });

  registry.registerNodeType({
    type: 'animation/getAnimationTime',
    category: 'Animation',
    description: '获取动画的时间信息',
    constructor: GetAnimationTimeNode,
    icon: '⏰',
    color: '#9C27B0'
  });

  registry.registerNodeType({
    type: 'animation/setAnimationTime',
    category: 'Animation',
    description: '设置动画播放时间',
    constructor: SetAnimationTimeNode,
    icon: '⏰',
    color: '#673AB7'
  });

  registry.registerNodeType({
    type: 'animation/isAnimationPlaying',
    category: 'Animation',
    description: '检查动画播放状态',
    constructor: IsAnimationPlayingNode,
    icon: '❓',
    color: '#607D8B'
  });

  // 动画混合节点
  registry.registerNodeType({
    type: 'animation/blendAnimation',
    category: 'Animation',
    description: '混合两个动画',
    constructor: AnimationBlendNode,
    icon: '🔀',
    color: '#795548'
  });

  registry.registerNodeType({
    type: 'animation/crossFadeAnimation',
    category: 'Animation',
    description: '在两个动画之间进行交叉淡化',
    constructor: CrossFadeAnimationNode,
    icon: '🌅',
    color: '#E91E63'
  });

  registry.registerNodeType({
    type: 'animation/layeredAnimation',
    category: 'Animation',
    description: '在基础动画上叠加分层动画',
    constructor: LayeredAnimationNode,
    icon: '📚',
    color: '#3F51B5'
  });

  registry.registerNodeType({
    type: 'animation/additiveAnimation',
    category: 'Animation',
    description: '叠加动画到基础动画',
    constructor: AdditiveAnimationNode,
    icon: '➕',
    color: '#8BC34A'
  });

  registry.registerNodeType({
    type: 'animation/maskAnimation',
    category: 'Animation',
    description: '使用遮罩控制动画影响范围',
    constructor: MaskAnimationNode,
    icon: '🎭',
    color: '#FF5722'
  });

  registry.registerNodeType({
    type: 'animation/blendTree',
    category: 'Animation',
    description: '多动画混合树',
    constructor: BlendTreeNode,
    icon: '🌳',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'animation/ikSolver',
    category: 'Animation',
    description: 'IK反向动力学求解器',
    constructor: IKSolverNode,
    icon: '🦴',
    color: '#9C27B0'
  });

  registry.registerNodeType({
    type: 'animation/lookAtConstraint',
    category: 'Animation',
    description: '注视约束控制',
    constructor: LookAtConstraintNode,
    icon: '👁️',
    color: '#2196F3'
  });

  registry.registerNodeType({
    type: 'animation/retargetAnimation',
    category: 'Animation',
    description: '动画重定向到不同骨架',
    constructor: RetargetAnimationNode,
    icon: '🎯',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'animation/boneTransform',
    category: 'Animation',
    description: '骨骼变换控制',
    constructor: BoneTransformNode,
    icon: '🦴',
    color: '#795548'
  });

  registry.registerNodeType({
    type: 'animation/morphTarget',
    category: 'Animation',
    description: '变形目标控制',
    constructor: MorphTargetNode,
    icon: '🔄',
    color: '#607D8B'
  });

  registry.registerNodeType({
    type: 'animation/facialAnimation',
    category: 'Animation',
    description: '面部动画控制',
    constructor: FacialAnimationNode,
    icon: '😊',
    color: '#E91E63'
  });

  registry.registerNodeType({
    type: 'animation/proceduralAnimation',
    category: 'Animation',
    description: '程序化动画生成',
    constructor: ProceduralAnimationNode,
    icon: '⚙️',
    color: '#3F51B5'
  });

  registry.registerNodeType({
    type: 'animation/animationEvent',
    category: 'Animation',
    description: '动画事件监听',
    constructor: AnimationEventNode,
    icon: '📢',
    color: '#FF5722'
  });
}
