/**
 * 动画节点预设
 * 提供各种动画控制和处理节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { EventNode } from '../nodes/EventNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 播放动画节点
 */
export class PlayAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '播放动画';
    this.metadata.description = '播放指定的动画';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '目标实体',
      required: true
    });

    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画名称',
      required: true
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环',
      defaultValue: false
    });

    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '播放速度',
      defaultValue: 1.0
    });

    // 输出插槽
    this.addOutput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '动画实例'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const entity = inputs.entity;
      const animationName = inputs.animationName;
      const loop = inputs.loop || false;
      const speed = inputs.speed || 1.0;

      if (!entity || !animationName) {
        console.error('PlayAnimationNode: 缺少必要的输入参数');
        return 'failed';
      }

      // 获取动画组件
      const animationComponent = entity.getComponent('Animation');
      if (!animationComponent) {
        console.error('PlayAnimationNode: 实体没有动画组件');
        return 'failed';
      }

      // 播放动画
      const animationInstance = animationComponent.play(animationName, {
        loop: loop,
        speed: speed
      });

      if (animationInstance) {
        this.setOutputValue('animationInstance', animationInstance);
        return 'success';
      } else {
        console.error(`PlayAnimationNode: 无法播放动画 ${animationName}`);
        return 'failed';
      }
    } catch (error) {
      console.error('PlayAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 停止动画节点
 */
export class StopAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '停止动画';
    this.metadata.description = '停止指定的动画';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'target',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例或实体',
      required: true
    });

    this.addInput({
      name: 'animationName',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '动画名称（可选）'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const target = inputs.target;
      const animationName = inputs.animationName;

      if (!target) {
        console.error('StopAnimationNode: 缺少目标参数');
        return 'failed';
      }

      // 如果目标是动画实例
      if (target.stop && typeof target.stop === 'function') {
        target.stop();
        return 'success';
      }

      // 如果目标是实体
      const animationComponent = target.getComponent && target.getComponent('Animation');
      if (animationComponent) {
        if (animationName) {
          animationComponent.stop(animationName);
        } else {
          animationComponent.stopAll();
        }
        return 'success';
      }

      console.error('StopAnimationNode: 无效的目标类型');
      return 'failed';
    } catch (error) {
      console.error('StopAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 暂停动画节点
 */
export class PauseAnimationNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '暂停动画';
    this.metadata.description = '暂停或恢复动画播放';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    this.addInput({
      name: 'paused',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '暂停状态',
      defaultValue: true
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animationInstance = inputs.animationInstance;
      const paused = inputs.paused;

      if (!animationInstance) {
        console.error('PauseAnimationNode: 缺少动画实例');
        return 'failed';
      }

      if (paused) {
        if (animationInstance.pause && typeof animationInstance.pause === 'function') {
          animationInstance.pause();
        } else {
          console.error('PauseAnimationNode: 动画实例不支持暂停');
          return 'failed';
        }
      } else {
        if (animationInstance.resume && typeof animationInstance.resume === 'function') {
          animationInstance.resume();
        } else if (animationInstance.play && typeof animationInstance.play === 'function') {
          animationInstance.play();
        } else {
          console.error('PauseAnimationNode: 动画实例不支持恢复');
          return 'failed';
        }
      }

      return 'success';
    } catch (error) {
      console.error('PauseAnimationNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 设置动画速度节点
 */
export class SetAnimationSpeedNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置动画速度';
    this.metadata.description = '设置动画播放速度';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    this.addInput({
      name: 'speed',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '速度倍率',
      defaultValue: 1.0
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animationInstance = inputs.animationInstance;
      const speed = inputs.speed || 1.0;

      if (!animationInstance) {
        console.error('SetAnimationSpeedNode: 缺少动画实例');
        return 'failed';
      }

      if (animationInstance.setSpeed && typeof animationInstance.setSpeed === 'function') {
        animationInstance.setSpeed(speed);
        return 'success';
      } else if (animationInstance.speed !== undefined) {
        animationInstance.speed = speed;
        return 'success';
      } else {
        console.error('SetAnimationSpeedNode: 动画实例不支持设置速度');
        return 'failed';
      }
    } catch (error) {
      console.error('SetAnimationSpeedNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 获取动画时间节点
 */
export class GetAnimationTimeNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '获取动画时间';
    this.metadata.description = '获取动画的时间信息';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    // 输出插槽
    this.addOutput({
      name: 'currentTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '当前时间'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '总时长'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '进度百分比'
    });
  }

  public execute(): any {
    try {
      const animationInstance = this.getInputValue('animationInstance');

      if (!animationInstance) {
        console.error('GetAnimationTimeNode: 缺少动画实例');
        return null;
      }

      const currentTime = animationInstance.currentTime || 0;
      const duration = animationInstance.duration || 0;
      const progress = duration > 0 ? (currentTime / duration) * 100 : 0;

      this.setOutputValue('currentTime', currentTime);
      this.setOutputValue('duration', duration);
      this.setOutputValue('progress', progress);

      return {
        currentTime,
        duration,
        progress
      };
    } catch (error) {
      console.error('GetAnimationTimeNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 设置动画时间节点
 */
export class SetAnimationTimeNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '设置动画时间';
    this.metadata.description = '设置动画播放时间';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    this.addInput({
      name: 'time',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '时间',
      defaultValue: 0
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animationInstance = inputs.animationInstance;
      const time = inputs.time || 0;

      if (!animationInstance) {
        console.error('SetAnimationTimeNode: 缺少动画实例');
        return 'failed';
      }

      if (animationInstance.setTime && typeof animationInstance.setTime === 'function') {
        animationInstance.setTime(time);
        return 'success';
      } else if (animationInstance.currentTime !== undefined) {
        animationInstance.currentTime = time;
        return 'success';
      } else {
        console.error('SetAnimationTimeNode: 动画实例不支持设置时间');
        return 'failed';
      }
    } catch (error) {
      console.error('SetAnimationTimeNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 检查动画播放状态节点
 */
export class IsAnimationPlayingNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '检查动画状态';
    this.metadata.description = '检查动画播放状态';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animationInstance',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画实例',
      required: true
    });

    // 输出插槽
    this.addOutput({
      name: 'isPlaying',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否播放中'
    });

    this.addOutput({
      name: 'isPaused',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否暂停'
    });

    this.addOutput({
      name: 'isLooping',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否循环'
    });
  }

  public execute(): any {
    try {
      const animationInstance = this.getInputValue('animationInstance');

      if (!animationInstance) {
        console.error('IsAnimationPlayingNode: 缺少动画实例');
        return null;
      }

      const isPlaying = animationInstance.isPlaying || false;
      const isPaused = animationInstance.isPaused || false;
      const isLooping = animationInstance.isLooping || false;

      this.setOutputValue('isPlaying', isPlaying);
      this.setOutputValue('isPaused', isPaused);
      this.setOutputValue('isLooping', isLooping);

      return {
        isPlaying,
        isPaused,
        isLooping
      };
    } catch (error) {
      console.error('IsAnimationPlayingNode: 执行错误', error);
      return null;
    }
  }
}

/**
 * 动画混合节点
 */
export class AnimationBlendNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed']
    });

    this.metadata.name = '动画混合';
    this.metadata.description = '混合两个动画';
    this.metadata.category = 'Animation';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'animation1',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画1',
      required: true
    });

    this.addInput({
      name: 'animation2',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '动画2',
      required: true
    });

    this.addInput({
      name: 'blendWeight',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '混合权重 (0-1)',
      defaultValue: 0.5
    });

    this.addInput({
      name: 'blendMode',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '混合模式',
      defaultValue: 'linear'
    });

    // 输出插槽
    this.addOutput({
      name: 'blendedAnimation',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '混合结果'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const animation1 = inputs.animation1;
      const animation2 = inputs.animation2;
      const blendWeight = Math.max(0, Math.min(1, inputs.blendWeight || 0.5));
      const blendMode = inputs.blendMode || 'linear';

      if (!animation1 || !animation2) {
        console.error('AnimationBlendNode: 缺少动画输入');
        return 'failed';
      }

      // 创建混合动画实例
      const blendedAnimation = {
        animation1,
        animation2,
        blendWeight,
        blendMode,
        isBlended: true,
        play: () => {
          animation1.play();
          animation2.play();
        },
        stop: () => {
          animation1.stop();
          animation2.stop();
        },
        pause: () => {
          animation1.pause();
          animation2.pause();
        },
        setWeight: (weight: number) => {
          blendedAnimation.blendWeight = Math.max(0, Math.min(1, weight));
        }
      };

      this.setOutputValue('blendedAnimation', blendedAnimation);
      return 'success';
    } catch (error) {
      console.error('AnimationBlendNode: 执行错误', error);
      return 'failed';
    }
  }
}

/**
 * 注册动画节点到节点注册表
 */
export function registerAnimationNodes(registry: any): void {
  // 基础动画节点
  registry.registerNodeType({
    type: 'animation/playAnimation',
    category: 'Animation',
    description: '播放指定的动画',
    constructor: PlayAnimationNode,
    icon: '▶️',
    color: '#4CAF50'
  });

  registry.registerNodeType({
    type: 'animation/stopAnimation',
    category: 'Animation',
    description: '停止指定的动画',
    constructor: StopAnimationNode,
    icon: '⏹️',
    color: '#F44336'
  });

  registry.registerNodeType({
    type: 'animation/pauseAnimation',
    category: 'Animation',
    description: '暂停或恢复动画播放',
    constructor: PauseAnimationNode,
    icon: '⏸️',
    color: '#FF9800'
  });

  registry.registerNodeType({
    type: 'animation/setAnimationSpeed',
    category: 'Animation',
    description: '设置动画播放速度',
    constructor: SetAnimationSpeedNode,
    icon: '⚡',
    color: '#2196F3'
  });

  registry.registerNodeType({
    type: 'animation/getAnimationTime',
    category: 'Animation',
    description: '获取动画的时间信息',
    constructor: GetAnimationTimeNode,
    icon: '⏰',
    color: '#9C27B0'
  });

  registry.registerNodeType({
    type: 'animation/setAnimationTime',
    category: 'Animation',
    description: '设置动画播放时间',
    constructor: SetAnimationTimeNode,
    icon: '⏰',
    color: '#673AB7'
  });

  registry.registerNodeType({
    type: 'animation/isAnimationPlaying',
    category: 'Animation',
    description: '检查动画播放状态',
    constructor: IsAnimationPlayingNode,
    icon: '❓',
    color: '#607D8B'
  });

  // 动画混合节点
  registry.registerNodeType({
    type: 'animation/blendAnimation',
    category: 'Animation',
    description: '混合两个动画',
    constructor: AnimationBlendNode,
    icon: '🔀',
    color: '#795548'
  });
}
