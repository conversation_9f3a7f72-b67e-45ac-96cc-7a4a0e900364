/**
 * AI节点预设
 * 提供各种AI和机器学习节点
 */

import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { EventNode } from '../nodes/EventNode';
import { NodeOptions, SocketType, SocketDirection } from '../nodes/Node';

/**
 * 路径寻找节点
 */
export class PathfindingNode extends FlowNode {
  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed', 'pathFound']
    });

    this.metadata.name = '路径寻找';
    this.metadata.description = 'A*路径寻找算法';
    this.metadata.category = 'AI';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'startPosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '起始位置',
      required: true
    });

    this.addInput({
      name: 'targetPosition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '目标位置',
      required: true
    });

    this.addInput({
      name: 'navMesh',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '导航网格'
    });

    this.addInput({
      name: 'algorithm',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '算法类型 (astar, dijkstra, greedy)',
      defaultValue: 'astar'
    });

    // 输出插槽
    this.addOutput({
      name: 'path',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '路径点列表'
    });

    this.addOutput({
      name: 'pathLength',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '路径长度'
    });

    this.addOutput({
      name: 'computeTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '计算时间（毫秒）'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const startPosition = inputs.startPosition;
      const targetPosition = inputs.targetPosition;
      const navMesh = inputs.navMesh;
      const algorithm = inputs.algorithm || 'astar';

      if (!startPosition || !targetPosition) {
        console.error('PathfindingNode: 缺少起始位置或目标位置');
        return 'failed';
      }

      const startTime = performance.now();
      const pathResult = this.findPath(startPosition, targetPosition, navMesh, algorithm);
      const computeTime = performance.now() - startTime;

      if (pathResult.success) {
        this.setOutputValue('path', pathResult.path);
        this.setOutputValue('pathLength', pathResult.length);
        this.setOutputValue('computeTime', computeTime);
        return 'pathFound';
      } else {
        return 'failed';
      }
    } catch (error) {
      console.error('PathfindingNode: 执行错误', error);
      return 'failed';
    }
  }

  private findPath(start: any, target: any, navMesh: any, algorithm: string): any {
    // 简化的A*算法实现
    switch (algorithm) {
      case 'astar':
        return this.astarPathfinding(start, target, navMesh);
      case 'dijkstra':
        return this.dijkstraPathfinding(start, target, navMesh);
      case 'greedy':
        return this.greedyPathfinding(start, target, navMesh);
      default:
        return { success: false, path: [], length: 0 };
    }
  }

  private astarPathfinding(start: any, target: any, navMesh: any): any {
    // 简化的A*实现
    const path = [start, target];
    const length = this.calculateDistance(start, target);
    return { success: true, path, length };
  }

  private dijkstraPathfinding(start: any, target: any, navMesh: any): any {
    // 简化的Dijkstra实现
    const path = [start, target];
    const length = this.calculateDistance(start, target);
    return { success: true, path, length };
  }

  private greedyPathfinding(start: any, target: any, navMesh: any): any {
    // 简化的贪心算法实现
    const path = [start, target];
    const length = this.calculateDistance(start, target);
    return { success: true, path, length };
  }

  private calculateDistance(pos1: any, pos2: any): number {
    const dx = pos2.x - pos1.x;
    const dy = pos2.y - pos1.y;
    const dz = (pos2.z || 0) - (pos1.z || 0);
    return Math.sqrt(dx * dx + dy * dy + dz * dz);
  }
}

/**
 * 行为树节点
 */
export class BehaviorTreeNode extends FlowNode {
  private behaviorTree: any = null;

  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['success', 'failed', 'running']
    });

    this.metadata.name = '行为树';
    this.metadata.description = '行为树AI系统';
    this.metadata.category = 'AI';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'treeDefinition',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '行为树定义',
      required: true
    });

    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '执行上下文'
    });

    this.addInput({
      name: 'deltaTime',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '时间增量',
      defaultValue: 0.016
    });

    // 输出插槽
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '执行结果'
    });

    this.addOutput({
      name: 'currentNode',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '当前执行节点'
    });

    this.addOutput({
      name: 'executionData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '执行数据'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const treeDefinition = inputs.treeDefinition;
      const context = inputs.context || {};
      const deltaTime = inputs.deltaTime || 0.016;

      if (!treeDefinition) {
        console.error('BehaviorTreeNode: 缺少行为树定义');
        return 'failed';
      }

      // 初始化行为树
      if (!this.behaviorTree) {
        this.behaviorTree = this.createBehaviorTree(treeDefinition);
      }

      // 执行行为树
      const result = this.executeBehaviorTree(this.behaviorTree, context, deltaTime);

      this.setOutputValue('result', result.status);
      this.setOutputValue('currentNode', result.currentNode);
      this.setOutputValue('executionData', result.data);

      return result.status;
    } catch (error) {
      console.error('BehaviorTreeNode: 执行错误', error);
      return 'failed';
    }
  }

  private createBehaviorTree(definition: any): any {
    return {
      root: definition.root,
      nodes: definition.nodes || {},
      blackboard: {},
      currentNode: null
    };
  }

  private executeBehaviorTree(tree: any, context: any, deltaTime: number): any {
    // 简化的行为树执行
    const rootNode = tree.nodes[tree.root];
    if (!rootNode) {
      return { status: 'failed', currentNode: null, data: {} };
    }

    tree.currentNode = rootNode;
    const result = this.executeNode(rootNode, tree, context, deltaTime);

    return {
      status: result,
      currentNode: tree.currentNode,
      data: tree.blackboard
    };
  }

  private executeNode(node: any, tree: any, context: any, deltaTime: number): string {
    switch (node.type) {
      case 'sequence':
        return this.executeSequence(node, tree, context, deltaTime);
      case 'selector':
        return this.executeSelector(node, tree, context, deltaTime);
      case 'action':
        return this.executeAction(node, tree, context, deltaTime);
      case 'condition':
        return this.executeCondition(node, tree, context, deltaTime);
      default:
        return 'failed';
    }
  }

  private executeSequence(node: any, tree: any, context: any, deltaTime: number): string {
    for (const childId of node.children || []) {
      const child = tree.nodes[childId];
      const result = this.executeNode(child, tree, context, deltaTime);
      if (result !== 'success') {
        return result;
      }
    }
    return 'success';
  }

  private executeSelector(node: any, tree: any, context: any, deltaTime: number): string {
    for (const childId of node.children || []) {
      const child = tree.nodes[childId];
      const result = this.executeNode(child, tree, context, deltaTime);
      if (result === 'success') {
        return 'success';
      }
    }
    return 'failed';
  }

  private executeAction(node: any, tree: any, context: any, deltaTime: number): string {
    // 执行具体动作
    console.log(`Executing action: ${node.name}`);
    return node.result || 'success';
  }

  private executeCondition(node: any, tree: any, context: any, deltaTime: number): string {
    // 检查条件
    const condition = node.condition || (() => true);
    return condition(context, tree.blackboard) ? 'success' : 'failed';
  }
}

/**
 * 状态机节点
 */
export class StateMachineNode extends FlowNode {
  private stateMachine: any = null;

  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['stateChanged', 'stateUpdated']
    });

    this.metadata.name = '状态机';
    this.metadata.description = '有限状态机';
    this.metadata.category = 'AI';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'states',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '状态定义',
      required: true
    });

    this.addInput({
      name: 'transitions',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '状态转换规则',
      required: true
    });

    this.addInput({
      name: 'initialState',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '初始状态',
      required: true
    });

    this.addInput({
      name: 'trigger',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '触发事件'
    });

    this.addInput({
      name: 'context',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '上下文数据'
    });

    // 输出插槽
    this.addOutput({
      name: 'currentState',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '当前状态'
    });

    this.addOutput({
      name: 'previousState',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '前一个状态'
    });

    this.addOutput({
      name: 'stateData',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '状态数据'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const states = inputs.states;
      const transitions = inputs.transitions;
      const initialState = inputs.initialState;
      const trigger = inputs.trigger;
      const context = inputs.context || {};

      if (!states || !transitions || !initialState) {
        console.error('StateMachineNode: 缺少状态机定义');
        return null;
      }

      // 初始化状态机
      if (!this.stateMachine) {
        this.stateMachine = {
          states: states,
          transitions: transitions,
          currentState: initialState,
          previousState: null,
          context: context
        };
      }

      let outputFlow = 'stateUpdated';

      // 处理状态转换
      if (trigger) {
        const newState = this.processTransition(this.stateMachine, trigger, context);
        if (newState && newState !== this.stateMachine.currentState) {
          this.stateMachine.previousState = this.stateMachine.currentState;
          this.stateMachine.currentState = newState;
          outputFlow = 'stateChanged';
        }
      }

      // 更新状态
      this.updateState(this.stateMachine, context);

      this.setOutputValue('currentState', this.stateMachine.currentState);
      this.setOutputValue('previousState', this.stateMachine.previousState);
      this.setOutputValue('stateData', this.stateMachine.states[this.stateMachine.currentState]);

      return outputFlow;
    } catch (error) {
      console.error('StateMachineNode: 执行错误', error);
      return null;
    }
  }

  private processTransition(stateMachine: any, trigger: string, context: any): string | null {
    const currentState = stateMachine.currentState;
    const stateTransitions = stateMachine.transitions[currentState];

    if (!stateTransitions) return null;

    for (const transition of stateTransitions) {
      if (transition.trigger === trigger) {
        // 检查转换条件
        if (!transition.condition || transition.condition(context)) {
          return transition.target;
        }
      }
    }

    return null;
  }

  private updateState(stateMachine: any, context: any): void {
    const currentStateData = stateMachine.states[stateMachine.currentState];
    if (currentStateData && currentStateData.update) {
      currentStateData.update(context);
    }
  }
}

/**
 * 决策树节点
 */
export class DecisionTreeNode extends FunctionNode {
  constructor(options?: Partial<NodeOptions>) {
    super(options);

    this.metadata.name = '决策树';
    this.metadata.description = '决策树分类器';
    this.metadata.category = 'AI';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'treeModel',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '决策树模型',
      required: true
    });

    this.addInput({
      name: 'inputFeatures',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '输入特征',
      required: true
    });

    // 输出插槽
    this.addOutput({
      name: 'prediction',
      type: SocketType.DATA,
      dataType: 'any',
      direction: SocketDirection.OUTPUT,
      description: '预测结果'
    });

    this.addOutput({
      name: 'confidence',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '置信度'
    });

    this.addOutput({
      name: 'decisionPath',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '决策路径'
    });
  }

  public execute(): any {
    try {
      const treeModel = this.getInputValue('treeModel');
      const inputFeatures = this.getInputValue('inputFeatures');

      if (!treeModel || !inputFeatures) {
        console.error('DecisionTreeNode: 缺少决策树模型或输入特征');
        return null;
      }

      const result = this.predict(treeModel, inputFeatures);

      this.setOutputValue('prediction', result.prediction);
      this.setOutputValue('confidence', result.confidence);
      this.setOutputValue('decisionPath', result.path);

      return result;
    } catch (error) {
      console.error('DecisionTreeNode: 执行错误', error);
      return null;
    }
  }

  private predict(tree: any, features: any[]): any {
    const path: string[] = [];
    let currentNode = tree.root;
    let confidence = 1.0;

    while (currentNode) {
      if (currentNode.isLeaf) {
        return {
          prediction: currentNode.value,
          confidence: confidence * currentNode.confidence,
          path: path
        };
      }

      const featureValue = features[currentNode.featureIndex];
      const decision = this.evaluateCondition(featureValue, currentNode.condition, currentNode.threshold);

      path.push(`${currentNode.featureName} ${currentNode.condition} ${currentNode.threshold}`);

      if (decision) {
        currentNode = currentNode.left;
        confidence *= 0.9; // 简化的置信度计算
      } else {
        currentNode = currentNode.right;
        confidence *= 0.8;
      }
    }

    return {
      prediction: null,
      confidence: 0,
      path: path
    };
  }

  private evaluateCondition(value: any, condition: string, threshold: any): boolean {
    switch (condition) {
      case '<=':
        return value <= threshold;
      case '<':
        return value < threshold;
      case '>=':
        return value >= threshold;
      case '>':
        return value > threshold;
      case '==':
        return value === threshold;
      case '!=':
        return value !== threshold;
      default:
        return false;
    }
  }
}

/**
 * 神经网络节点
 */
export class NeuralNetworkNode extends FlowNode {
  private network: any = null;

  constructor(options?: Partial<NodeOptions>) {
    super({
      ...options,
      outputFlowNames: ['predicted', 'trained', 'failed']
    });

    this.metadata.name = '神经网络';
    this.metadata.description = '简单神经网络';
    this.metadata.category = 'AI';
  }

  protected initializeSockets(): void {
    super.initializeSockets();

    // 输入插槽
    this.addInput({
      name: 'operation',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '操作类型 (predict, train, create)',
      required: true
    });

    this.addInput({
      name: 'networkConfig',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.INPUT,
      description: '网络配置'
    });

    this.addInput({
      name: 'inputData',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '输入数据'
    });

    this.addInput({
      name: 'targetData',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.INPUT,
      description: '目标数据（训练用）'
    });

    this.addInput({
      name: 'learningRate',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '学习率',
      defaultValue: 0.01
    });

    // 输出插槽
    this.addOutput({
      name: 'output',
      type: SocketType.DATA,
      dataType: 'array',
      direction: SocketDirection.OUTPUT,
      description: '网络输出'
    });

    this.addOutput({
      name: 'loss',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.OUTPUT,
      description: '损失值'
    });

    this.addOutput({
      name: 'networkState',
      type: SocketType.DATA,
      dataType: 'object',
      direction: SocketDirection.OUTPUT,
      description: '网络状态'
    });
  }

  protected process(inputs: Record<string, any>): string | null {
    try {
      const operation = inputs.operation;
      const networkConfig = inputs.networkConfig;
      const inputData = inputs.inputData;
      const targetData = inputs.targetData;
      const learningRate = inputs.learningRate || 0.01;

      if (!operation) {
        console.error('NeuralNetworkNode: 缺少操作类型');
        return 'failed';
      }

      switch (operation) {
        case 'create':
          this.network = this.createNetwork(networkConfig);
          this.setOutputValue('networkState', this.getNetworkState());
          return 'predicted';

        case 'predict':
          if (!this.network || !inputData) {
            console.error('NeuralNetworkNode: 缺少网络或输入数据');
            return 'failed';
          }
          const output = this.predict(inputData);
          this.setOutputValue('output', output);
          this.setOutputValue('networkState', this.getNetworkState());
          return 'predicted';

        case 'train':
          if (!this.network || !inputData || !targetData) {
            console.error('NeuralNetworkNode: 缺少训练数据');
            return 'failed';
          }
          const loss = this.train(inputData, targetData, learningRate);
          this.setOutputValue('loss', loss);
          this.setOutputValue('networkState', this.getNetworkState());
          return 'trained';

        default:
          console.error(`NeuralNetworkNode: 不支持的操作 ${operation}`);
          return 'failed';
      }
    } catch (error) {
      console.error('NeuralNetworkNode: 执行错误', error);
      return 'failed';
    }
  }

  private createNetwork(config: any): any {
    const layers = config?.layers || [2, 4, 1];
    const network = {
      layers: layers,
      weights: [],
      biases: [],
      activations: []
    };

    // 初始化权重和偏置
    for (let i = 0; i < layers.length - 1; i++) {
      const weightMatrix = this.createMatrix(layers[i + 1], layers[i]);
      const biasVector = this.createVector(layers[i + 1]);

      // 随机初始化
      this.randomizeMatrix(weightMatrix);
      this.randomizeVector(biasVector);

      network.weights.push(weightMatrix);
      network.biases.push(biasVector);
    }

    return network;
  }

  private predict(input: number[]): number[] {
    let activation = [...input];

    for (let i = 0; i < this.network.weights.length; i++) {
      activation = this.matrixVectorMultiply(this.network.weights[i], activation);
      activation = this.vectorAdd(activation, this.network.biases[i]);
      activation = activation.map(x => this.sigmoid(x));
    }

    return activation;
  }

  private train(input: number[], target: number[], learningRate: number): number {
    // 简化的反向传播
    const output = this.predict(input);
    const loss = this.calculateLoss(output, target);

    // 这里应该实现完整的反向传播算法
    // 简化实现，只返回损失值

    return loss;
  }

  private calculateLoss(output: number[], target: number[]): number {
    let sum = 0;
    for (let i = 0; i < output.length; i++) {
      sum += Math.pow(output[i] - target[i], 2);
    }
    return sum / output.length;
  }

  private sigmoid(x: number): number {
    return 1 / (1 + Math.exp(-x));
  }

  private createMatrix(rows: number, cols: number): number[][] {
    return Array(rows).fill(0).map(() => Array(cols).fill(0));
  }

  private createVector(size: number): number[] {
    return Array(size).fill(0);
  }

  private randomizeMatrix(matrix: number[][]): void {
    for (let i = 0; i < matrix.length; i++) {
      for (let j = 0; j < matrix[i].length; j++) {
        matrix[i][j] = (Math.random() - 0.5) * 2;
      }
    }
  }

  private randomizeVector(vector: number[]): void {
    for (let i = 0; i < vector.length; i++) {
      vector[i] = (Math.random() - 0.5) * 2;
    }
  }

  private matrixVectorMultiply(matrix: number[][], vector: number[]): number[] {
    const result = [];
    for (let i = 0; i < matrix.length; i++) {
      let sum = 0;
      for (let j = 0; j < vector.length; j++) {
        sum += matrix[i][j] * vector[j];
      }
      result.push(sum);
    }
    return result;
  }

  private vectorAdd(a: number[], b: number[]): number[] {
    return a.map((val, i) => val + b[i]);
  }

  private getNetworkState(): any {
    if (!this.network) return null;

    return {
      layers: this.network.layers,
      totalWeights: this.network.weights.reduce((sum: number, layer: number[][]) =>
        sum + layer.reduce((layerSum: number, row: number[]) => layerSum + row.length, 0), 0),
      totalBiases: this.network.biases.reduce((sum: number, layer: number[]) => sum + layer.length, 0)
    };
  }
}

/**
 * 注册AI节点到节点注册表
 */
export function registerAINodes(registry: any): void {
  // 路径寻找节点
  registry.registerNodeType({
    type: 'ai/pathfinding',
    category: 'AI',
    description: 'A*路径寻找算法',
    constructor: PathfindingNode,
    icon: '🗺️',
    color: '#4CAF50'
  });

  // 行为树节点
  registry.registerNodeType({
    type: 'ai/behaviorTree',
    category: 'AI',
    description: '行为树AI系统',
    constructor: BehaviorTreeNode,
    icon: '🌳',
    color: '#2196F3'
  });

  // 状态机节点
  registry.registerNodeType({
    type: 'ai/stateMachine',
    category: 'AI',
    description: '有限状态机',
    constructor: StateMachineNode,
    icon: '⚙️',
    color: '#FF9800'
  });

  // 决策树节点
  registry.registerNodeType({
    type: 'ai/decisionTree',
    category: 'AI',
    description: '决策树分类器',
    constructor: DecisionTreeNode,
    icon: '🌲',
    color: '#9C27B0'
  });

  // 神经网络节点
  registry.registerNodeType({
    type: 'ai/neuralNetwork',
    category: 'AI',
    description: '简单神经网络',
    constructor: NeuralNetworkNode,
    icon: '🧠',
    color: '#3F51B5'
  });
}
